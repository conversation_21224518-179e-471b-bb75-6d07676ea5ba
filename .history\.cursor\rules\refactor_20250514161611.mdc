---
description: 
globs: *.cs
alwaysApply: false
---
---
description:代码清洗描述
globs:["*.cs"]
alwaysApply: true
---
# 开发环境
- Unity2019.4.40f1

# 代码清洗原则
- 不要新增和删减功能
- 如果修改了方法，要保证调用该方法的地方同样被修改。
- 清洗之前，先生成测试用例，清洗之后的代码需要通过全部的测试用例。
- 清洗的时候分模块来处理。
- 双引号内的内容不用修改，保持原来的内容。
- 清洗的过程中，不要修改文件的路径，保持原来的架构风格
- 不要自动提交代码，如果涉及到提交代码，先找我确认。
- 最后测试通过了，可以删除测试的代码和文件。 
- 清洗过后的代码不能和原来代码存在过多相似的地方。需要判定为两套代码。
- 清洗之后，需要对清洗之后的模块生成对应的使用文档。
- 清洗之后，扫描整个工程，对修改之前引用的代码修改成正确的引用。
- 在清洗的过程中，不要修改文件名。
- 不要随意修改命名空间
- 如果变量是被序列化的，修改了变量名之后，要去修改工程里面所有prefab里面的变量名的引用
- 使用C# 7 及以下兼容写法
