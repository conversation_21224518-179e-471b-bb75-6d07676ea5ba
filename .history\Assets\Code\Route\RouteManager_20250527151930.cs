using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using RG.Common;
using Pooling;
using RG.Characters;
using RG.Routes.Builders;
using RG.Level;
using RG.Themes;

namespace RG.Routes
{
    [PrefabPathAttribute("Prefabs/Managers/RouteManager")]
    public class RouteManager : SingletonMonobehavior<RouteManager>
    {
        private const string RoutePoolName = "Routes";
        public const int RouteBuilderHeight = 90;
        public const int FirstRouteStartPosZ = 0;

        public static string PrefabPath = "Assets/Resources/Prefabs/Managers/RouteManager.prefab";

        public int PrevSegmentVariation = 0;

        public event EventHandler<RouteCreatedEventArgs> OnRouteCreated;
        public event EventHandler<CharacterRouteChangedEventArgs> OnCharacterRouteChanged;
        public event EventHandler<RouteTransitionEventArgs> OnRouteTransitionStarted;

        [SerializeField] private Route _defaultRoute;
        public Route DefaultRoute
        {
            get
            {
                return _defaultRoute;
            }
        }

        [SerializeField]
        [Range(0, 600)]
        private int _alternateRouteFirstOfferTime;

        [SerializeField]
        [Range(10, 600)]
        private int _alternateRouteRepeatedOfferDelay;

        [Range(0, 60)]
        [SerializeField] private int _alternateRouteRepeatedOfferDelayIncrement;

        [SerializeField] private bool _limitAlternateOfferToHeadstart = false;

        [SerializeField] private ValueResetEvent _alternateRouteOfferDelayResetEvent = ValueResetEvent.GameStart;
        [SerializeField] private List<AlternateRouteConfig> _alternateRouteConfigs;

        [SerializeField]
        private Route _benchmarkRoute;

        public static bool UseBenchmarkRoute { private get; set; }

        private Game _game;
        private CharacterBase _character;

        private Route _mainRoute;
        private Route _characterRoute;
        private CharacterChunks _characterChunksNear = new CharacterChunks();

        private RouteCollection _routeCollection;
        private Vector3 _originPosition;

        private List<AlternateRouteConfig> _alternateRouteConfigsEnabled = new List<AlternateRouteConfig>();

        private float _alternateRouteRepeatedOfferDelayIncremented;
        private float _alternateRouteOfferDefaultRouteTimeNext;
        private bool _isWaitingForRouteChoice;

        public bool isInLoadingLevel { get; set; }

        private class RoutePool
        {
            private readonly Queue<Route> _pool = new Queue<Route>();
            private readonly Func<Route> _createFunc;
            private readonly Action<Route> _onGet;
            private readonly Action<Route> _onRelease;
            private readonly Action<Route> _onDestroy;
            private readonly int _maxSize;
            private readonly HashSet<Route> _activeRoutes = new HashSet<Route>();

            public RoutePool(Func<Route> createFunc, Action<Route> onGet, Action<Route> onRelease, Action<Route> onDestroy, int maxSize)
            {
                _createFunc = createFunc;
                _onGet = onGet;
                _onRelease = onRelease;
                _onDestroy = onDestroy;
                _maxSize = maxSize;
            }

            public Route Get()
            {
                Route route = null;
                while (_pool.Count > 0)
                {
                    route = _pool.Dequeue();
                    if (route != null && route.gameObject != null)
                    {
                        break;
                    }
                }

                if (route == null || route.gameObject == null)
                {
                    route = _createFunc();
                }

                _activeRoutes.Add(route);
                _onGet?.Invoke(route);
                return route;
            }

            public void Release(Route route)
            {
                if (route == null || route.gameObject == null)
                {
                    return;
                }

                _activeRoutes.Remove(route);

                if (_pool.Count < _maxSize)
                {
                    _onRelease?.Invoke(route);
                    _pool.Enqueue(route);
                }
                else
                {
                    _onDestroy?.Invoke(route);
                }
            }

            public void Clear()
            {
                foreach (var route in _activeRoutes)
                {
                    if (route != null && route.gameObject != null)
                    {
                        _onDestroy?.Invoke(route);
                    }
                }
                _activeRoutes.Clear();
                _pool.Clear();
            }
        }

        private readonly Dictionary<RouteType, RoutePool> _routePools = new Dictionary<RouteType, RoutePool>();
        private readonly List<Route> _routesToUpdate = new List<Route>();
        private float _lastUpdateTime;
        private const float UPDATE_INTERVAL = 0.1f; // 每0.1秒更新一次

        protected override void Initialize()
        {
            isInLoadingLevel = true;
            _game = Game.Instance;

            if (GlobalFlags.Instance.ROUTE_BUILDER_PREVIEW == false)
            {
                if (_game == null)
                {
                    Debug.LogError("game is null");
                }
                _character = _game.Character;
            }

            _alternateRouteOfferDefaultRouteTimeNext = _alternateRouteFirstOfferTime;
            _alternateRouteRepeatedOfferDelayIncremented = _alternateRouteRepeatedOfferDelay - _alternateRouteRepeatedOfferDelayIncrement;

            FilterAlternateRouteConfigs();

            _routeCollection = new RouteCollection(this);

            ThemeManager.Instance.Theme.InitializeEnvironmentTransitions();

            UseBenchmarkRoute = false;

            InitializeRoutePools();
        }

        private void InitializeRoutePools()
        {
            foreach (RouteType routeType in Enum.GetValues(typeof(RouteType)))
            {
                var routePrefab = GetRoutePrefab(routeType);
                if (routePrefab != null)
                {
                    _routePools[routeType] = new RoutePool(
                        () => Instantiate(routePrefab),
                        route => route.gameObject.SetActive(true),
                        route => route.gameObject.SetActive(false),
                        route => Destroy(route.gameObject),
                        100
                    );
                }
            }
        }

        private void FilterAlternateRouteConfigs(IList<RouteType> allowedRouteTypes = null)
        {
            _alternateRouteConfigsEnabled.Clear();
            foreach (var ar in _alternateRouteConfigs)
            {
                if (ar.Enabled &&
                    (allowedRouteTypes == null || allowedRouteTypes.Contains(ar.Route.Type)))
                {
                    _alternateRouteConfigsEnabled.Add(ar);
                }
            }
        }

        public void Reset()
        {
            Debug.Log("RouteManager Reset");

            _routeCollection.RemoveAll();
            _characterRoute = null;
            _mainRoute = null;
            _originPosition = Vector3.forward * FirstRouteStartPosZ;

            _isWaitingForRouteChoice = false;

            if (_alternateRouteOfferDelayResetEvent == ValueResetEvent.GameStart)
            {
                _alternateRouteOfferDefaultRouteTimeNext = _alternateRouteFirstOfferTime;
                _alternateRouteRepeatedOfferDelayIncremented = _alternateRouteRepeatedOfferDelay - _alternateRouteRepeatedOfferDelayIncrement;
            }

            _mainRoute = CreateRoute(RouteType.PVP, SectionSpawnMode.All, ChunkBuildMode.Normal, _originPosition);
            _characterChunksNear.Reset();
            ChangeCharacterRoute(_mainRoute);

            UpdateRoutes(0);
        }

        public void CleanRoute()
        {
            _routeCollection.RemoveAll();
            _characterRoute = null;
            _mainRoute = null;
            _originPosition = Vector3.forward * FirstRouteStartPosZ;
            _isWaitingForRouteChoice = false;
        }

        public void ReStart(RouteType type = RouteType.Default)
        {
            this.CleanRoute();

            PrevSegmentVariation = 0;

            if (_alternateRouteOfferDelayResetEvent == ValueResetEvent.GameStart)
            {
                _alternateRouteOfferDefaultRouteTimeNext = _alternateRouteFirstOfferTime;
                _alternateRouteRepeatedOfferDelayIncremented = _alternateRouteRepeatedOfferDelay - _alternateRouteRepeatedOfferDelayIncrement;
            }

            _mainRoute = CreateRoute(type, SectionSpawnMode.All, ChunkBuildMode.Normal, _originPosition);
            _characterChunksNear.Reset();
            ChangeCharacterRoute(_mainRoute);

            UpdateRoutes(0);

        }

        public void Clear()
        {
            _routeCollection.RemoveAll();
            foreach (var pool in _routePools.Values)
            {
                pool.Clear();
            }
            StopUpdate(true);
        }

        public void StopUpdate(bool stopupdate)
        {
            _isChangingTheme = stopupdate;
        }

        private bool _isChangingTheme = false;

        private bool IsPaused { get { return _game == null; } }

        public ICollection<Route> Routes { get { return _routeCollection.Routes; } }

        public Route MainRoute
        {
            get { return _mainRoute; }
            private set
            {
                _mainRoute = value;
            }
        }

        public Route CharacterRoute { get { return _characterRoute; } }

        public CharacterChunks CharacterChunksNear
        {
            get { return _characterChunksNear; }
        }

        public bool IsCharacterInAnyRoute { get { return _characterRoute != null; } }

        private float AlternateRouteOfferDefaultRouteTimeNext
        {
            get { return _alternateRouteOfferDefaultRouteTimeNext; }
            set
            {
                _alternateRouteOfferDefaultRouteTimeNext = value;
            }
        }

        void Update()
        {
            //is in game
            if (!Game.Instance.IsInGame.Value) return;

            if (IsPaused || _isChangingTheme)
                return;

            float currentTime = Time.time;
            if (currentTime - _lastUpdateTime < UPDATE_INTERVAL)
                return;

            _lastUpdateTime = currentTime;
            UpdateCharacterRoute();
            UpdateRoutesOptimized();
        }

        private void UpdateRoutesOptimized()
        {
            float characterZ = GetFirstCharacterWorldZ();
            _routesToUpdate.Clear();
            _routesToUpdate.AddRange(_routeCollection.Routes);

            for (int i = 0; i < _routesToUpdate.Count; i++)
            {
                Route route = _routesToUpdate[i];
                if (!route.CharacterEntered && !route.CharacterSkippedEntry &&
                    GamePositionRoot.Instance.GetGamePosition(route.transform).z < _character.GamePosition.z)
                {
                    route.NotifyCharacterEntrySkipped();
                }
            }

            UpdateRoutes(characterZ);
        }

        public float GetFirstCharacterWorldZ()
        {
            float maxZ = Character.Instance.GamePosition.z;
            foreach (var dummy in RobotMgr.Instance.RobotMap.Values)
            {
                float tmp = dummy.GamePosition.z;
                if (tmp > maxZ)
                {
                    maxZ = tmp;
                }
            }
            return maxZ;
        }

        public float GetRoutePositionOffsetY(RouteHeight routeHeight)
        {
            float routePositionOffsetY = 0;
            switch (routeHeight)
            {
                case RouteHeight.Neg1:
                    routePositionOffsetY = -RouteBuilderHeight;
                    break;
                case RouteHeight.Pos0:
                    routePositionOffsetY = 0;
                    break;
                case RouteHeight.Pos1:
                    routePositionOffsetY = RouteBuilderHeight;
                    break;
                default:
                    throw new Exception(routeHeight.ToString());
                    break;
            }
            return routePositionOffsetY;
        }

        private void UpdateRoutes(float characterPositionZ)
        {
            _routeCollection.Update(characterPositionZ, _game.RandomGenerator);
        }

        private void UpdateCharacterRoute()
        {
            var characterPos = _character.GamePosition;
            var characterRoute = _routeCollection.GetRouteContainingPoint(characterPos);

            if (characterRoute != _characterRoute)
            {
                ChangeCharacterRoute(characterRoute);
            }

            UpdateCharacterNearChunks(_characterChunksNear, characterPos, characterRoute);
        }

        private void UpdateCharacterNearChunks(CharacterChunks charChunks, Vector3 charPosition, Route charRoute)
        {
            if (charRoute == null)
                return;

            var currentChunk = charRoute.Builder.GetChunkContainingPoint(charPosition);

            if (currentChunk == null)
                return;

            if (charChunks.Current != currentChunk)
            {
                charChunks.Previous = _characterChunksNear.Current;
                charChunks.Current = currentChunk;
                charChunks.Next = currentChunk.Next;
            }

            if (charChunks.Next != charChunks.Current.Next)
            {
                charChunks.Next = charChunks.Current.Next;
            }
        }

        private void ChangeCharacterRoute(Route route)
        {
            Route nextCharacterRoute = route;

            if (nextCharacterRoute == null)
            {
                nextCharacterRoute = MainRoute;
            }

            if (_characterRoute != nextCharacterRoute)
            {
                Route previousCharacterRoute = _characterRoute;
                _characterRoute = nextCharacterRoute;

                if (previousCharacterRoute != null)
                {
                    ProcessCharacterRouteExited(previousCharacterRoute, _characterRoute);
                }

                if (_characterRoute != null)
                {
                    ProcessCharacterRouteEnter(_characterRoute, previousCharacterRoute);
                }

                RaiseOnCharacterRouteChanged(this, new CharacterRouteChangedEventArgs(previousCharacterRoute, _characterRoute));
            }
        }

        private void ProcessCharacterRouteEnter(Route route, Route previousRoute)
        {
            var characterRouteAltRoute = route as AlternateRoute;
            if (characterRouteAltRoute != null)
            {
                _isWaitingForRouteChoice = false;
                _alternateRouteRepeatedOfferDelayIncremented += _alternateRouteRepeatedOfferDelayIncrement;

                if (characterRouteAltRoute.Builder.IsDone)
                {
                    SetNextAlternateRouteOfferTimeFromPosition(characterRouteAltRoute.Builder.SpawnedLeadingEdgeGamePositionZ);
                }
            }

            route.NotifyCharacterEntered(_character, previousRoute);
        }

        private void ProcessCharacterRouteExited(Route route, Route nextRoute)
        {
            route.NotifyCharacterExited(nextRoute);
        }

        public Route GetRoutePrefab(RouteType routeType)
        {
            if (UseBenchmarkRoute)
            {
                return _benchmarkRoute;
            }

            Route route = _defaultRoute;

            if (_defaultRoute.Type == routeType)
            {
                route = _defaultRoute;
            }
            else if (routeType != RouteType.Default)
            {
                route = FindAlternateRoute(r => r.Type == routeType);
            }

            if (route == null)
            {
                Debug.LogWarning("CreateRouteBuilder() Will not create: '" + routeType + "' because it is missing in inspector.", this);
                return null;
            }

            return route;
        }

        private Route FindAlternateRoute(Predicate<Route> predicate)
        {
            var routeConfig = _alternateRouteConfigsEnabled.Find(rc => predicate(rc.Route));
            return routeConfig != null ? routeConfig.Route : null;
        }

        public Route CreateRoute(RouteType routeType, SectionSpawnMode sectionSpawnMode,
            ChunkBuildMode chunkBuildMode, Vector3 originPosition)
        {
            if (_routePools.TryGetValue(routeType, out var pool))
            {
                var route = pool.Get();
                route.transform.position = originPosition;
                route.Initialize(this, originPosition, sectionSpawnMode, chunkBuildMode, RouteType.None, _game.RandomGenerator);
                route.OnDestructing += Route_OnDestructing;

                var altRoute = route as AlternateRoute;
                if (altRoute != null)
                {
                    altRoute.OnCharacterEntrySkipped += AlternateRoute_CharacterEntrySkipped;
                    altRoute.Builder.OnCompletedLayingSections += AlternateRouteBuilder_OnCompletedLayingSections;
                }

                _routeCollection.Add(route);

                if (OnRouteCreated != null)
                {
                    OnRouteCreated(this, new RouteCreatedEventArgs(route));
                }

                return route;
            }
            return null;
        }

        private float ComputeDefaultRouteTimeUntilPosition(float gamePositionZ)
        {
            float defaultRouteSpeedEvalTime = _game.OffsetGameStartTime;
            float defaultRouteDistance = gamePositionZ - _character.GamePosition.z;

            var characterAlternateRoute = CharacterRoute as AlternateRoute;
            if (characterAlternateRoute != null && characterAlternateRoute.Builder.IsDoneLayingSections)
            {
                float altRouteEndPositionZ = characterAlternateRoute.Builder.SpawnedLeadingEdgeGamePositionZ;
                float distanceCharacterToRouteEnd = Math.Max(0f, altRouteEndPositionZ - _character.GamePosition.z);
                float timeUntilAltRouteEnd = EvaluatePlayerDistanceTravelTime(distanceCharacterToRouteEnd);

                defaultRouteSpeedEvalTime += timeUntilAltRouteEnd;
                defaultRouteDistance -= distanceCharacterToRouteEnd;
            }

            if (defaultRouteDistance < 0)
                return 0f;

            float defaultRouteSpeed = _game.CalculateSpeed(defaultRouteSpeedEvalTime);
            return defaultRouteDistance / defaultRouteSpeed;
        }

        private float EvaluatePlayerDistanceTravelTime(float distanceFromCharacter)
        {
            if (Character.Instance.CurrentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                return distanceFromCharacter / RouteConstants.WIDTH_PER_LANE;
            }
            return distanceFromCharacter / Character.Instance.CurrentSpeed;
        }


        private void SetNextAlternateRouteOfferTimeFromPosition(float positionZ)
        {
            float timeInDefaultRouteUntilPosition = ComputeDefaultRouteTimeUntilPosition(positionZ);
            AlternateRouteOfferDefaultRouteTimeNext = timeInDefaultRouteUntilPosition + _alternateRouteRepeatedOfferDelayIncremented;
        }

        private void RaiseOnCharacterRouteChanged(object sender, CharacterRouteChangedEventArgs args)
        {
            if (OnCharacterRouteChanged != null)
            {
                OnCharacterRouteChanged(this, args);
            }
        }

        private void Route_OnDestructing(object sender, EventArgs args)
        {
            if (sender is Route route)
            {
                route.OnDestructing -= Route_OnDestructing;
                route.OnCharacterEntrySkipped -= AlternateRoute_CharacterEntrySkipped;
                if (route.Builder != null)
                {
                    route.Builder.OnCompletedLayingSections -= AlternateRouteBuilder_OnCompletedLayingSections;
                }

                if (_routePools.TryGetValue(route.Type, out var pool))
                {
                    pool.Release(route);
                }
                else
                {
                    Destroy(route.gameObject);
                }
            }
        }

        private void AlternateRouteBuilder_OnCompletedLayingSections(object sender, EventArgs args)
        {
            var altRouteBuilder = (RouteBuilder)sender;

            if (!_isWaitingForRouteChoice)
            {
                SetNextAlternateRouteOfferTimeFromPosition(altRouteBuilder.SpawnedLeadingEdgeGamePositionZ);
            }
        }

        private void AlternateRoute_CharacterEntrySkipped(object sender, Route.CharacterEntrySkippedEventArgs args)
        {
            var altRoute = (AlternateRoute)sender;

            _isWaitingForRouteChoice = false;
            SetNextAlternateRouteOfferTimeFromPosition(altRoute.OriginTransform.position.z);
        }

        public enum ValueResetEvent
        {
            AppStart,
            GameStart,
            RunRevive
        }

        public class CharacterChunks
        {
            public RouteChunk Previous;
            public RouteChunk Current;
            public RouteChunk Next;

            public void Reset()
            {
                Previous = Current = Next = null;
            }

            public bool Contains(RouteChunk chunk)
            {
                return Previous == chunk || Current == chunk || Next == chunk;
            }
        }

        [Serializable]
        public class AlternateRouteConfig
        {
            public AlternateRoute Route;
            public bool Enabled = true;
            public int Weight = 1;
        }

        #region Events
        public class RouteCreatedEventArgs : EventArgs
        {
            public readonly Route Route;
            public RouteCreatedEventArgs(Route route) { Route = route; }
        }

        public class RouteTransitionEventArgs : System.EventArgs
        {
            public readonly Route CurrentRoute;
            public readonly Route TransitionRoute;
            public readonly RouteType TransitionRouteType;

            public bool ExistsTransitionRoute { get { return TransitionRoute != null; } }

            public RouteTransitionEventArgs(Route currentRoute, RouteType transitionRouteType, Route transitionRoute)
            {
                CurrentRoute = currentRoute;
                TransitionRouteType = transitionRouteType;
                TransitionRoute = transitionRoute;
            }

            public override string ToString()
            {
                return string.Format("[RouteTransitionSkippedEventArgs (Current={0}, TransutionType={1}, TransutionRoute={2})]",
                    CurrentRoute, TransitionRoute, TransitionRoute);
            }

        }

        public class CharacterRouteChangedEventArgs : System.EventArgs
        {
            public Route FromRoute { get { return _fromRoute; } }
            private Route _fromRoute;

            public bool HasFromRoute { get { return _fromRoute != null; } }

            public Route ToRoute { get { return _toRoute; } }
            private Route _toRoute;

            public CharacterRouteChangedEventArgs(Route fromRoute, Route toRoute)
            {
                _fromRoute = fromRoute;
                _toRoute = toRoute;
            }

            public override string ToString()
            {
                return string.Format("[CharacterRouteTypeChangedEventArgs (FromRouteType={0}, ToRouteType={1})]", FromRoute, ToRoute);
            }
        }

        #endregion
    }
}
