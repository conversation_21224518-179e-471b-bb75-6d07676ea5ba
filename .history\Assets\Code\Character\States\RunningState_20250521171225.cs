using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG;
using RG.CharacterData;
using RG.Common.Logger;
using CodeStage.AntiCheat.ObscuredTypes;

namespace RG.Characters.States
{
    /// <summary>
    /// 角色运行状态
    /// Character running state
    /// </summary>
    public class RunningState : CharacterState
    {
        [HideInInspector]
        public float cameraFOV = 40f;

        public bool transitionFromHeight = false;

        private float tunnelStartZ;
        private Curve offsetDeltaCurve = null;

        public const float CHARACTER_CHANGE_TRACK_LENGTH = 30;

        public const float DEFAULT_FOV = 40f;

        public RunPositions currentRunPosition = RunPositions.ground;
        public enum RunPositions { ground, station, train, movingTrain, air };

        private Queue<Collider> GrindedTrains = new Queue<Collider>();
        private int GrindedTrainsBufferSize = 5;

        public static System.Action RunStarted;
        public static System.Action RunEnded;

        private static RunningState instance = null;
        protected override void Awake()
        {
            base.Awake();

            cameraFOV = DEFAULT_FOV;

            _character.OnStumble += (string stumbleAniType, StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, string colliderName, bool safeStumble) =>
            {
                _character.CharacterCamera.Shake();
            };

            _character.OnLanding += UpdateGroundTag;
        }

        private void _updateSpeed()
        {
            _character.SetCurrentSpeed(_game.Speed);
        }

        /// <summary>
        /// 进入运行状态
        /// Enter running state
        /// </summary>
        public override void Enter()
        {
            RunStarted.SafeInvoke();
            // 实现进入运行状态的逻辑
            // Implement logic for entering the running state
            _character.charSpeed.Begin();

            transitionFromHeight = false;
            bool transitionWithRoll = false;

            if (_character.GamePosition.y > 70f && _character.GamePosition.y < 125f)
            {
                transitionFromHeight = true;
            }

            _character.CharacterTrigger.enabled = true;

            _character.LastGroundedY = _character.GamePosition.y;

            var setAnimationSpeedEvent = new AnimationEvent();
            setAnimationSpeedEvent.functionName = "SetAnimationSpeedEvent";
            setAnimationSpeedEvent.time = 0.1f;
            setAnimationSpeedEvent.messageOptions = SendMessageOptions.RequireReceiver;
        }

        /// <summary>
        /// 退出运行状态
        /// Exit running state
        /// </summary>
        public override void Exit()
        {
            RunEnded.SafeInvoke();
            // 实现退出运行状态的逻辑
            // Implement logic for exiting the running state
        }

        /// <summary>
        /// 更新运行状态
        /// Update running state
        /// </summary>
        public override void Update()
        {
            _character.runningTime += Time.deltaTime;

            Vector3 position = _character.TransformPosition;

            if (position.y < 0)
            {
                position.y = 1f;
                _character.transform.position = position;
                _character.CachePositions();
            }

            if (_character.Game == null || UnityEngine.Time.timeScale == 0)
                return;

            // 检查是否卡住（静止不动）
            _character.CheckHitTrigger();

            // 更新模型旋转
            var a = _character.GetModelRotation();
            var b = Quaternion.Euler(0f, a.eulerAngles.y, a.eulerAngles.z);
            _character.SetModelRotation(Quaternion.RotateTowards(a, b, 90f * Time.deltaTime));

            // 处理角落绊倒
            if (_character._lastCornerStumbleCollider != null && _character._lastCornerStumbleTime + CharacterConstant.CORNER_MIN_HIT_TIME < Time.time)
            {
                _character._lastCornerStumbleCollider = null;
            }

            _character._lastDistance = _character.GamePosition.z;

            this._updateSpeed();
            if (_character.isDropMoving)
            {
                _character.MoveDrop();
            }
            else if (!_character.isStopRunning)
            {
                ApplyGravity();
                MoveForward();
            }

            if (transitionFromHeight)
            {
                if (_character.IsGrounded.Value || _character.inAirJump && _characterController.isGrounded)
                {
                    transitionFromHeight = false;

                    _character.ForceLeaveSubway();
                }
            }

            _character.CheckInAirJump();

            UpdateInAirRunPosition();

            if (_character.GamePosition.z > PvpMgr.ROUTE_LENGTH)
            {
                Character.Instance.charSpeed.ChangeState(CharacterSpeedState.Idle);
                NetDirector.Rule_PostZ(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position);
                NetDirector.PostRoom_EndGame(10, PhotonNetwork.player.ID, RG.XPluginAccount._userid, true);
                Game.Instance.ChangeState(GameStateType.Die);
                NetDirector.Post_GetRankIdx(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position.z, RG.XPluginAccount._userid);
            }
            else if (PvpMgr.Inst.IsEnterEndGame)
            {
                if (PvpMgr.Inst.EnterEndGameTime - PhotonNetwork.time < 0)
                {
                    Character.Instance.charSpeed.ChangeState(CharacterSpeedState.Idle);

                    Game.Instance.ChangeState(GameStateType.End);
                }
            }
        }

        /// <summary>
        /// FixedUpdate 更新运行状态
        /// FixedUpdate for running state
        /// </summary>
        public override void FixedUpdate()
        {
            // 应用重力
            ApplyGravity();

            // 向前移动
            MoveForward();

            // 实现运行状态的 FixedUpdate 更新逻辑
            // Implement fixed update logic for the running state
        }

        /// <summary>
        /// 向前移动
        /// Move forward
        /// </summary>
        public void MoveForward()
        {
            Vector3 gamePosition = _character.GamePosition;
            float nextGamePositionZ = gamePosition.z + _character.CurrentSpeed * Time.deltaTime;
            Vector3 verticalMove = Vector3.zero;
            bool canMoveUp = true;
            if (_character.IsJumping && _character.JumpUpperCollision)
            {
                canMoveUp = false;
                if (!_character.JumpUpperCollisionEvaded && _character.JumpUpperCollisionColliderMinY < _character.CharacterTrigger.bounds.max.y)
                {
                    if (0f <= _character.VerticalSpeed)
                        verticalMove = -_character.Gravity * Time.deltaTime * Time.deltaTime * Vector3.up;
                }
                else
                {
                    verticalMove = Vector3.zero;
                    _character.JumpUpperCollisionEvaded = true;
                }
            }

            if (_character.VerticalSpeed < 0f || canMoveUp)
                verticalMove = _character.VerticalSpeed * Time.deltaTime * Vector3.up;

            if (_character.IsJumping && _character.SuperSneakersJump.HasValue)
            {
                SuperSneakersJump jump = _character.SuperSneakersJump.Value;

                if (_character.GamePosition.z < jump.EndGamePositionZ)
                {
                    if (canMoveUp)
                    {
                        float y_new = _character.superSneakersJumpCurve.Evaluate((nextGamePositionZ - jump.StartGamePositionZ) / jump.Length) * _character.SuperJumpAltitude + jump.StartGamePositionY;
                        float y_delta = y_new - gamePosition.y;
                        verticalMove = Vector3.up * y_delta;
                    }
                    else
                        verticalMove = Vector3.zero;
                }
                else
                {
                    _character._superSneakersJump = null;
                    _character.VerticalSpeed = 0f;
                    verticalMove = Vector3.zero;
                }
            }
            Vector3 xzPositionTarget;
            xzPositionTarget = Route.GetPosition(_character.X, nextGamePositionZ);
            Vector3 xzPosition = new Vector3(gamePosition.x, 0f, gamePosition.z);
            Vector3 xzMove = xzPositionTarget - xzPosition;

            if (_character.CharacterController.enabled)
            {
                _character.CharacterController.Move(verticalMove + xzMove);
            }
            else
            {
                _character.transform.position = _character.TransformPosition + xzMove;
            }

            _character.CachePositions();

            if (_character.CharacterController.isGrounded)
            {
                _character.LastGroundedY = gamePosition.y;
            }
            _character._movedSinceLastFixedUpdate = true;
        }

        /// <summary>
        /// 应用重力
        /// Apply gravity
        /// </summary>
        public void ApplyGravity()
        {
            if (_character.VerticalSpeed < 0f && _character.CharacterController.isGrounded)
            {
                if (_character.StartedJumpFromGround && _character.TrainJump && _character.IsRunningOnGround())
                {
                    _character.SendJumpOverTrainEvent();
                }

                if (!_character.IsRunningAir())
                {
                    _character._startedJumpFromGround = false;
                }

                _character.IsGrounded.Value = true;

                if (_character.IsJumping || _character.IsFalling)
                {
                    _character._jumpUpperCollision = false;
                    _character._jumpUpperCollisionEvaded = true;

                    _character.IsJumping = false;
                    _character.IsFalling = false;
                    _character.NotifyOnLanding();
                }

                _character.VerticalSpeed = 0f;
            }
            else
            {
                if (_character.StartedJumpFromGround && _character.TrainJumpSampleZ < _character.GamePosition.z)
                {
                    RaycastHit hit;
                    if (Physics.Raycast(new Ray(_character.TransformPosition, -Vector3.up), out hit))
                    {
                        if (hit.collider.CompareTag("HitMovingTrain") ||
                            hit.collider.CompareTag("HitTrain"))
                        {
                            _character._trainJump = true;
                        }
                    }
                    _character.TrainJumpSampleZ += CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }

            if (_character.IsFalling && _character.IsGliderboard)
            {
                if (_character.GlideTimeLeft < _character.GlideTime)
                {
                    _character.VerticalSpeed = _character._glideVerticalSpeed.Evaluate(_character.GlideTimeLeft / _character.GlideTime);
                    _character.GlideTimeLeft += Time.deltaTime;
                }
                else
                {
                    _character.VerticalSpeed -= _character.gliderGravity * Time.deltaTime;
                }
            }
            else
            {
                _character.VerticalSpeed -= _character.Gravity * Time.deltaTime;
            }

            if (!_character.CharacterController.isGrounded)
            {
                if (!_character.IsFalling && _character.VerticalSpeed < _character.VerticalFallSpeedLimit && !_character.IsRolling)
                {
                    _character.IsFalling = true;
                    _character.SendHangtimeEvent();
                    _character.IsGrounded.Value = false;
                }
            }
        }

        private void UpdateInAirRunPosition()
        {
            if (!_characterController.isGrounded)
                currentRunPosition = RunPositions.air;
        }

        private void LandedOnTrain(Collider trainCollider)
        {
            if (_character.BoardState.enabled)
            {
                if (!GrindedTrains.Contains(trainCollider))
                {
                    if (GrindedTrains.Count > GrindedTrainsBufferSize)
                        GrindedTrains.Dequeue();
                    GrindedTrains.Enqueue(trainCollider);
                }
            }
        }

        private void UpdateGroundTag(CharacterBase character)
        {
            var ray = new Ray(character.CharacterRoot.position, -Vector3.up);
            RaycastHit hitInfo;
            if (Physics.Raycast(ray, out hitInfo))
            {
                switch (hitInfo.collider.tag)
                {
                    case "Ground":
                        currentRunPosition = RunPositions.ground;
                        break;
                    case "HitTrain":
                        currentRunPosition = RunPositions.train;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "HitMovingTrain":
                        currentRunPosition = RunPositions.movingTrain;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "Station":
                        currentRunPosition = RunPositions.station;
                        break;
                    default:
                        break;
                }
            }
        }

        public override void HandleCriticalHit()
        {
            _character.CharacterCamera.Shake();
            if (_character.IsStumbling)
            {
                _character.StopStumble();
            }
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            if (Character.Instance.isStopRunning) return;
            if (PowerupMgr.Inst.IsInDevil)
            {
                switch (swipeDir)
                {
                    case SwipeDir.Up:
                        swipeDir = SwipeDir.Down;
                        break;
                    case SwipeDir.Down:
                        swipeDir = SwipeDir.Up;
                        break;
                    case SwipeDir.Left:
                        swipeDir = SwipeDir.Right;
                        break;
                    case SwipeDir.Right:
                        swipeDir = SwipeDir.Left;
                        break;
                    default:
                        break;
                }
            }

            float currentSpeed = _character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }
            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    if (_character.IsTeleboard)
                    {
                        _character.ChangeTrackBy(-1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        _character.ChangeTrackBy(-1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Right:
                    if (_character.IsTeleboard)
                    {
                        _character.ChangeTrackBy(+1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        _character.ChangeTrackBy(+1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Up:
                    Jump();
                    break;
                case SwipeDir.Down:

                    _character.Roll();

                    if (RouteManager.Instance.IsCharacterInAnyRoute)
                    {
                        Vector3 routeRelativeCharacterPosition = RouteManager.Instance.CharacterRoute.GetRelativePosition(_character.GamePosition);

                        if (routeRelativeCharacterPosition.y > 200)
                        {
                            RGLogger.LogWarning("Character", "Accellerated Fall");

                            _character.verticalSpeed = -_character.CalculateJumpVerticalSpeed(routeRelativeCharacterPosition.y);
                        }
                    }

                    break;
            }
        }

        private void Jump()
        {
            _character.Jump();
        }

        public static RunningState Instance
        {
            get
            {
                return instance ?? (instance = FindObjectOfType(typeof(RunningState)) as RunningState);
            }
        }
    }
}
