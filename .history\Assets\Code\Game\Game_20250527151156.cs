﻿#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG.Characters;
using RG.Characters.States;
using RG.Routes;
using System;
using RG.Level;
using CodeStage.AntiCheat.ObscuredTypes;
using CharacterData = RG.CharacterData;
using RG.Common;
using RG.Common.Logger;
using Random = RG.Common.Utilities.Random;

namespace RG
{
    public class Game : PvpLogicBehaviour
    {
        [SerializeField] private SpeedInfo _speedInfoCfg;
        [SerializeField] public AudioStateLoop AudioStateLoop;
        [SerializeField] public bool IsOnOpeningShot = true;
        [SerializeField] public Transform OpeningShotRoot;
        [SerializeField] public Transform CameraRoot;
        [SerializeField] public Camera OpeningShotCamera;
        [SerializeField] public Animation OpeningShotAnim;

        public static bool IsInstantiated { get { return _instance != null; } }
        private static Game _instance;
        public static Game Instance { get => _instance ?? (_instance = Utility.FindObject<Game>()); }

        private Dictionary<GameStateType, GameState> _stateCfg;
        private IEnumerator _gameStateCoroutine;
        private GameStateType _gameStateType;
        private ObscuredFloat _gameStartTime = 0f;

        public float OffsetGameStartTime { get => TimeMgr.time - _gameStartTime; }
        public Random RandomGenerator { get; } = new Random();
        public EncryptedSpeedInfo SpeedInfo = new EncryptedSpeedInfo();

        private CharacterState _characterState;
        public CharacterState CharacterState
        {
            get { return _characterState; }
            set
            {
                CharacterState oldCharacterState = _characterState;
                if (oldCharacterState != null)
                {
                    oldCharacterState.End().MoveNext();
                }

                _characterState = value;
                if (_characterState != null)
                {
                    _gameStateCoroutine = _characterState.Begin();
                }

                OnCharacterStateChanged?.Invoke(this, new CharacterStateChangedEventArgs(oldCharacterState, _characterState));

            }
        }

        public CharacterModifierCollection Modifiers { get; private set; }
        public Character Character { get; private set; }
        public CharacterRendering CharacterRendering { get; private set; }
        public CharacterCamera CharacterCamera { get; private set; }
        public RunningState Running { get; internal set; }
        public float ElapsedGameDuration { get; private set; }

        public event EventHandler<CharacterStateChangedEventArgs> OnCharacterStateChanged;
        public static Action OnStartGame;
        public static Action OnEndGame;
        public static Action OnMenu;
        public static Action OnIntro;
        public static Action OnReady;
        public static Action OnEnd;
        public ValueChangeNotifier<bool> IsInGame;
        public ValueChangeNotifier<bool> IsInTopMenu;
        //是否已经进过游戏了
        public bool IsStartRun = false;
        //是否是第一次登录游戏
        public bool IsFirstLogin = true;

        private GameInput _gameInput;
        private ObscuredFloat _speed = 30;
        public float Speed { get => _speed; }
        public void UpdateSpeedDuration(float duration)
        {
            _speed = CalculateSpeed(duration);
        }

        public float CalculateSpeed(float duration)
        {
            if (duration < SpeedInfo.rampUpDuration)
            {
                float rampUpFactor = duration / SpeedInfo.rampUpDuration;
                return Mathf.Lerp(SpeedInfo.min, SpeedInfo.max, rampUpFactor);
            }

            return Mathf.Max(SpeedInfo.max, SpeedInfo.min);
        }

        private void ResetSpeed()
        {
            SpeedInfo.min = _speedInfoCfg.min;
            SpeedInfo.max = _speedInfoCfg.max;
            SpeedInfo.rampUpDuration = _speedInfoCfg.rampUpDuration;
            UpdateSpeedDuration(0);
        }

        public Game()
        {
            IsInGame = new ValueChangeNotifier<bool>(false);
            IsInTopMenu = new ValueChangeNotifier<bool>(false);
        }

        public void Awake()
        {
            ResetSpeed();

            Character = Character.Instance;
            Character.Initialize();
            CharacterRendering = Character.CharacterRendering;
            CharacterCamera = CharacterCamera.Instance;

            // 延迟初始化 Running 状态
            StartCoroutine(InitializeRunningState());

            Modifiers = new CharacterModifierCollection();
            _gameInput = new GameInput(this);
            InitStateCfg();
            OnCharacterStateChanged += CharacterCamera.Game_OnCharacterStateChanged;
        }

        private IEnumerator InitializeRunningState()
        {
            // 等待 Character 和 CharacterStateManager 初始化完成
            int maxAttempts = 10;
            int attempts = 0;
            
            while (Running == null && attempts < maxAttempts)
            {
                if (Character != null && Character.CharacterStateManager != null)
                {
                    Running = Character.GetState<RunningState>();
                    if (Running != null)
                    {
                        RGLogger.Log("Game", "成功初始化 Running 状态");
                        break;
                    }
                }
                
                attempts++;
                yield return new WaitForSeconds(0.1f);
            }

            if (Running == null)
            {
                RGLogger.LogError("Game", "无法初始化 Running 状态，请检查 Character 和 CharacterStateManager 是否正确初始化");
            }
        }

        private void InitStateCfg()
        {
            _stateCfg = new Dictionary<GameStateType, GameState>()
            {
                  {GameStateType.Opening, new GameOpeningState(this)},
                  {GameStateType.Menu, new GameMenuState(this)},
                  {GameStateType.Intro, new GameIntroState(this)},
                  {GameStateType.Ready, new GameReadyState(this)},
                  {GameStateType.Die, new GameDieState(this)},
                  {GameStateType.End, new GameEndState(this)},
                  {GameStateType.Disconnect, new GameDisconnectState(this)},
            };
        }

        protected override void Start()
        {
            base.Start();

            ChangeState(GameStateType.Menu);
            _gameStateCoroutine.MoveNext();
        }

        public void ChangeState(GameStateType gameStateType)
        {
            CharacterState = null;
            if (_gameStateType == gameStateType) return;

            _gameStateType = gameStateType;
            _gameStateCoroutine = _stateCfg.TryGetValue(gameStateType, out GameState state) ? state.Execute() : null;
            Debug.Log("ChangeState:" + gameStateType);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            _gameInput.Destroy();
            _gameInput = null;

            if (CharacterCamera != null)
            {
                OnCharacterStateChanged -= CharacterCamera.Game_OnCharacterStateChanged;
            }
        }

        public void StartGame()
        {
            // 确保所有组件都已初始化
            if (!Character.Instance.IsInitialized)
            {
                Character.Instance.Initialize();
            }

            if (!CharacterRendering.Instance.IsInitialized)
            {
                CharacterRendering.Instance.Initialize();
            }

            if (!Hoverboard.Instance.IsInitialized)
            {
                Hoverboard.Instance.Initialize();
            }

            


            // 开始游戏逻辑
            IsInGame.Value = true;
            IsInTopMenu.Value = false;
            this.IsStartRun = true;
            GamePositionRoot.Instance.Clear();
            Character.Instance.charSpeed.Init(CharacterData.Manager.Inst.currentCharacterId);
            Character.Instance.CanAccelerateForward = true;
            Character.Instance.charSpeed.End();
            Character.Instance.OnEnterGame();
            Hoverboard.Instance.HardReset();
            Hoverboard.Instance.CheckCurrentHoverboardLoaded();
            SpawnerMgr.Inst.Init();
            PowerupMgr.Inst.Prepare();

            ChangeState(GameStateType.Opening);
            CharacterCamera.Instance.UpdateFOV();
            Character.CharacterStateManager.ChangeState<RunningState>();
            // 确保 Running 状态已正确设置
            if (Running == null)
            {
                RGLogger.Log("Game", "Running 状态为 null，尝试重新获取...");
                Running = GetRunningStateSafely();
            }
            OnStartGame?.Invoke();
        }

        protected override void LogicUpdate(int fpsCount)
        {
            // 更新游戏持续时间
            if (IsInGame.Value)
            {
                ElapsedGameDuration += TimeMgr.deltaTime;
            }

            if (!IsInTopMenu.Value)
            {
                UpdateSpeedDuration(TimeMgr.time - _gameStartTime);
            }
            else
            {
                _gameStartTime += TimeMgr.deltaTime;
            }

            _gameStateCoroutine?.MoveNext();
            if (_characterState != null)
            {
                Modifiers.Update();
            }
        }

        protected override void LogicLateUpdate(int fpsCount)
        {
#if UNITY_EDITOR
            SceneView sceneView = SceneView.lastActiveSceneView;
            if (sceneView && GameConfig.instance.IsLockSceneView)
            {
                sceneView.pivot = Character.TransformPosition;
            }
#endif
        }

        public void Intro()
        {
            SetPhotonTransformViewEnabled(true);
            ResetSpeed();
            _gameStartTime = TimeMgr.time;
        }

        public bool CheckEnd()
        {
            return _gameStateType == GameStateType.Die || _gameStateType == GameStateType.End;
        }

        public void End()
        {
            NetDirector.Post_GetPvpRank(XPluginAccount._userid);
            Character.Instance.OnExitGame(); // 结算时清理所有状态
            OnEnd?.Invoke();
        }

        public void Disconnected()
        {
            PvpMgr.Inst.StopHeart();
            CharacterRendering.CharacterModel.transform.localRotation = Quaternion.Euler(new Vector3(0.0f, 0.0f, 0.0f));
            PvpMgr.Inst.Clear();
            PvpMgr.Inst.BackToMainMenu();
            SpawnerMgr.Inst.Clear();
            PowerupMgr.Inst.Clear();
            Character.Instance.charSpeed.End();
            Character.Instance.charSpeed.Despawn();
            RobotMgr.Instance.ClearRobots();
            PvpDirector.Shared.LeaveBattle();

            Character.Instance.OnExitGame(); // 断线/回主界面时清理所有状态
            OnEnd?.Invoke();
        }

        public void SetPhotonTransformViewEnabled(bool value)
        {
            PhotonTransformView photonTransformView = Character.GetComponent<PhotonTransformView>();
            if (photonTransformView == null) return;

            photonTransformView.enabled = value;
        }

        private RunningState GetRunningStateSafely()
        {
            if (Character.Instance == null || Character.Instance.CharacterStateManager == null)
            {
                return null;
            }
            return Character.Instance.GetState<RunningState>();
        }
    }
}