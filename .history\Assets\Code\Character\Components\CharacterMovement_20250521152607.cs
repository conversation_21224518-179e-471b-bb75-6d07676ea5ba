using UnityEngine;
using RG.Common;
using RG.Routes;

namespace RG.Characters.Components
{
    public class CharacterMovement : MonoBehaviour
    {
        private CharacterBase _character;
        private CharacterController _controller;
        private float _verticalSpeed;
        private float _gravity = 200f;
        private Vector3 _currentPosition;
        private Vector3 _currentDistance;

        public void Initialize(CharacterBase character)
        {
            _character = character;
            _controller = character.CharacterController;
            _gravity = character.Gravity;
        }

        public void MoveForward()
        {
            Vector3 gamePosition = _character.GamePosition;
            float nextGamePositionZ = gamePosition.z + _character.CurrentSpeed * Time.deltaTime;
            Vector3 verticalMove = Vector3.zero;
            bool canMoveUp = true;

            if (_character.IsJumping && _character._jumpUpperCollision)
            {
                canMoveUp = false;
                if (!_character._jumpUpperCollisionEvaded && _character._jumpUpperCollisionColliderMinY < _character.CharacterTrigger.bounds.max.y)
                {
                    if (0f <= _verticalSpeed)
                        verticalMove = -_gravity * Time.deltaTime * Time.deltaTime * Vector3.up;
                }
                else
                {
                    verticalMove = Vector3.zero;
                    _character._jumpUpperCollisionEvaded = true;
                }
            }

            if (_verticalSpeed < 0f || canMoveUp)
                verticalMove = _verticalSpeed * Time.deltaTime * Vector3.up;

            Vector3 xzPositionTarget = Route.GetPosition(_character.x, nextGamePositionZ);
            Vector3 xzPosition = new Vector3(gamePosition.x, 0f, gamePosition.z);
            Vector3 xzMove = xzPositionTarget - xzPosition;

            if (_controller.enabled)
            {
                _controller.Move(verticalMove + xzMove);
            }
            else
            {
                transform.position = _character.TransformPosition + xzMove;
            }

            CachePositions();
        }

        public void ApplyGravity()
        {
            if (_verticalSpeed < 0f && _controller.isGrounded)
            {
                _verticalSpeed = 0f;
            }
            else
            {
                _verticalSpeed -= _gravity * Time.deltaTime;
            }
        }

        public void SetVerticalSpeed(float speed)
        {
            _verticalSpeed = speed;
        }

        public float GetVerticalSpeed()
        {
            return _verticalSpeed;
        }

        private void CachePositions()
        {
            _currentPosition = transform.position;
            _currentDistance = GamePositionRoot.Instance.GetGamePosition(transform);
        }
    }
} 