using UnityEngine;

namespace RG.Characters.Config
{
    /// <summary>
    /// 角色基础配置
    /// </summary>
    [CreateAssetMenu(fileName = "CharacterConfig", menuName = "Game/Character/Character Config")]
    public class CharacterConfig : ScriptableObject
    {
        [Header("角色信息")]
        [Tooltip("角色ID")]
        public string CharacterId;
        
        [Toolt<PERSON>("角色名称")]
        public string CharacterName;
        
        [Header("基础属性")]
        [Tooltip("基础移动速度")]
        public float BaseSpeed = 30f;
        
        [Tooltip("重力大小")]
        public float Gravity = 200f;
        
        [Header("跳跃相关")]
        [Tooltip("普通跳跃高度")]
        public float JumpHeight = 20f;
        
        [Toolt<PERSON>("超级跳跃高度")]
        public float SuperJumpHeight = 40f;
        
        [Tooltip("超级跳跃顶点比例")]
        [Range(0f, 1f)]
        public float SuperJumpApexRatio = 0.5f;
        
        [Header("滑行相关")]
        [Tooltip("滑行时间")]
        public float GlideTime = 0.1f;
        
        [<PERSON>lt<PERSON>("滑行重力")]
        public float GliderGravity = 0.01f;
        
        [Head<PERSON>("碰撞相关")]
        [Tooltip("角色控制器高度")]
        public float CharacterControllerHeight = 4f;
        
        [Tooltip("角色控制器中心点")]
        public Vector3 CharacterControllerCenter = new Vector3(0, 2f, 0);
        
        [Tooltip("角色触发器高度")]
        public float CharacterTriggerHeight = 4f;
        
        [Tooltip("角色触发器中心点")]
        public Vector3 CharacterTriggerCenter = new Vector3(0, 4f, 0);
        
        [Header("轨道相关")]
        [Tooltip("初始轨道索引")]
        public int InitialTrackIndex = 1;
        
        [Tooltip("轨道切换持续时间")]
        public float TrackChangeDuration = 0.3f;
        
        [Header("网络同步")]
        [Tooltip("位置同步频率")]
        public float PositionSyncFrequency = 0.1f;
        
        [Tooltip("状态同步频率")]
        public float StateSyncFrequency = 0.2f;
        
        [Header("性能优化")]
        [Tooltip("是否启用对象池")]
        public bool EnableObjectPool = true;
        
        [Tooltip("对象池初始大小")]
        public int ObjectPoolInitialSize = 10;
        
        [Tooltip("对象池最大大小")]
        public int ObjectPoolMaxSize = 50;
    }
} 