using UnityEngine;
using System;

namespace RG.Characters.Config
{
    /// <summary>
    /// 角色配置管理器
    /// </summary>
    public class CharacterConfigManager : MonoBehaviour
    {
        private static CharacterConfigManager _instance;
        public static CharacterConfigManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    GameObject go = new GameObject("CharacterConfigManager");
                    _instance = go.AddComponent<CharacterConfigManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        [SerializeField]
        private CharacterConfig _characterConfig;
        
        [SerializeField]
        private CharacterStateConfig _stateConfig;

        public CharacterConfig CharacterConfig => _characterConfig;
        public CharacterStateConfig StateConfig => _stateConfig;

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            LoadConfigs();
        }

        private void LoadConfigs()
        {
            // 加载基础配置
            if (_characterConfig == null)
            {
                _characterConfig = Resources.Load<CharacterConfig>("Config/CharacterConfig");
                if (_characterConfig == null)
                {
                    Debug.LogError("无法加载角色基础配置！");
                }
            }

            // 加载状态配置
            if (_stateConfig == null)
            {
                _stateConfig = Resources.Load<CharacterStateConfig>("Config/CharacterStateConfig");
                if (_stateConfig == null)
                {
                    Debug.LogError("无法加载角色状态配置！");
                }
            }
        }

        /// <summary>
        /// 获取状态转换配置
        /// </summary>
        public CharacterStateConfig.StateTransitionConfig GetStateTransition(string fromState, string toState)
        {
            return _stateConfig.StateTransitions.Find(t => 
                t.FromState == fromState && t.ToState == toState);
        }

        /// <summary>
        /// 获取状态行为配置
        /// </summary>
        public CharacterStateConfig.StateBehaviorConfig GetStateBehavior(string stateName)
        {
            return _stateConfig.StateBehaviors.Find(b => b.StateName == stateName);
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public bool ValidateConfigs()
        {
            bool isValid = true;

            // 验证基础配置
            if (_characterConfig == null)
            {
                Debug.LogError("角色基础配置为空！");
                isValid = false;
            }

            // 验证状态配置
            if (_stateConfig == null)
            {
                Debug.LogError("角色状态配置为空！");
                isValid = false;
            }

            // 验证状态转换配置
            if (_stateConfig.StateTransitions != null)
            {
                foreach (var transition in _stateConfig.StateTransitions)
                {
                    if (string.IsNullOrEmpty(transition.FromState) || 
                        string.IsNullOrEmpty(transition.ToState))
                    {
                        Debug.LogError("状态转换配置存在无效的状态名称！");
                        isValid = false;
                    }
                }
            }

            // 验证状态行为配置
            if (_stateConfig.StateBehaviors != null)
            {
                foreach (var behavior in _stateConfig.StateBehaviors)
                {
                    if (string.IsNullOrEmpty(behavior.StateName))
                    {
                        Debug.LogError("状态行为配置存在无效的状态名称！");
                        isValid = false;
                    }
                }
            }

            return isValid;
        }
    }
} 