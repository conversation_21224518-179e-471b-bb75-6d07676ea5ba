using UnityEngine;
using System;
using System.Collections.Generic;
using RG.Characters;
using RG.Common;

namespace RG.Characters.States
{
    /// <summary>
    /// 角色状态管理器
    /// </summary>
    public class CharacterStateManager
    {
        private CharacterBase _character;
        private Dictionary<Type, CharacterState> _stateMap = new Dictionary<Type, CharacterState>();
        private CharacterState _currentState;

        public CharacterState CurrentState => _currentState;

        public CharacterStateManager(CharacterBase character)
        {
            _character = character;
        }

        /// <summary>
        /// 注册状态
        /// </summary>
        public void RegisterState<T>() where T : CharacterState, new()
        {
            var state = _character.GetOrAdd<T>();
            state.Initialize(_character);
            _stateMap[typeof(T)] = state;
        }

        /// <summary>
        /// 切换状态 - 修复循环调用问题
        /// </summary>
        public void ChangeState<T>() where T : CharacterState
        {
            var nextState = _stateMap[typeof(T)];
            if (nextState != null)
            {
                // 检查是否可以切换状态
                if (_currentState == null || _currentState.CanTransitionTo(nextState))
                {
                    _currentState?.Exit();
                    _currentState = nextState;
                    _currentState.Enter();
                }
            }
        }

        /// <summary>
        /// 获取状态
        /// </summary>
        public T GetState<T>() where T : CharacterState
        {
            return _stateMap[typeof(T)] as T;
        }

        /// <summary>
        /// 批量初始化所有状态（进入跑酷时调用）
        /// </summary>
        public void InitAllStates()
        {
            foreach (var state in _stateMap.Values)
            {
                state.InitState();
            }
            ChangeState<RunningState>();
        }

        /// <summary>
        /// 批量清理所有状态（退出一局时调用）
        /// </summary>
        public void ClearAllStates()
        {
            foreach (var state in _stateMap.Values)
            {
                state.ClearState();
            }
        }
    }
}