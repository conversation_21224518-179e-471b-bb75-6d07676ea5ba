using System.Collections.Generic;
using UnityEngine;
using System;
using RG.Common;
using RG;
using System.Threading.Tasks;

namespace SubwayPvp.Core.Audio
{
    /// <summary>
    /// 音频资源加载器
    /// Audio resource loader
    /// </summary>
    public static class AudioClipLoader
    {
        private static readonly Dictionary<string, AudioClip> _clipCache = new Dictionary<string, AudioClip>();
        private static readonly Dictionary<string, Task<AudioClip>> _loadingTasks = new Dictionary<string, Task<AudioClip>>();
        private static readonly HashSet<string> _failedPaths = new HashSet<string>();

        /// <summary>
        /// 加载音频资源
        /// Load audio clip
        /// </summary>
        /// <param name="clipPath">资源路径 Resource path</param>
        /// <returns>音频片段 Audio clip</returns>
        public static AudioClip LoadClip(string clipPath)
        {
            if (string.IsNullOrEmpty(clipPath))
            {
                Debug.LogWarning("AudioClipLoader: Attempted to load clip with empty path");
                return null;
            }

            // 检查是否之前加载失败
            if (_failedPaths.Contains(clipPath))
            {
                Debug.LogWarning($"AudioClipLoader: Skipping previously failed clip at path {clipPath}");
                return null;
            }

            // 检查缓存
            if (_clipCache.TryGetValue(clipPath, out AudioClip cachedClip))
            {
                return cachedClip;
            }

            // 首先尝试使用 Resources 加载（向后兼容）
            AudioClip resourceClip = Resources.Load<AudioClip>(clipPath);
            if (resourceClip != null)
            {
                _clipCache[clipPath] = resourceClip;
                return resourceClip;
            }

            // 如果 Resources 加载失败，启动 Addressable 异步加载
            LoadClipAsync(clipPath);
            return null;
        }

        /// <summary>
        /// 异步加载音频资源
        /// Load audio clip asynchronously
        /// </summary>
        /// <param name="clipPath">资源路径或地址 Resource path or address</param>
        /// <returns>加载任务 Loading task</returns>
        public static Task<AudioClip> LoadClipAsync(string clipPath)
        {
            if (string.IsNullOrEmpty(clipPath))
            {
                Debug.LogWarning("AudioClipLoader: Attempted to load clip with empty path");
                return Task.FromResult<AudioClip>(null);
            }

            // 检查是否之前加载失败
            if (_failedPaths.Contains(clipPath))
            {
                Debug.LogWarning($"AudioClipLoader: Skipping previously failed clip at path {clipPath}");
                return Task.FromResult<AudioClip>(null);
            }

            // 检查缓存
            if (_clipCache.TryGetValue(clipPath, out AudioClip cachedClip))
            {
                return Task.FromResult(cachedClip);
            }

            // 检查是否正在加载
            if (_loadingTasks.TryGetValue(clipPath, out Task<AudioClip> existingTask))
            {
                return existingTask;
            }

            // 创建异步加载任务
            var loadTask = LoadWithAddressables(clipPath);
            _loadingTasks[clipPath] = loadTask;
            return loadTask;
        }

        private static async Task<AudioClip> LoadWithAddressables(string clipPath)
        {
            try
            {
                var tcs = new TaskCompletionSource<AudioClip>();
                
                AddressableMgr.Inst.Load<AudioClip>(clipPath, (bool success, AudioClip clip) =>
                {
                    if (success && clip != null)
                    {
                        _clipCache[clipPath] = clip;
                        tcs.SetResult(clip);
                    }
                    else
                    {
                        Debug.LogError($"AudioClipLoader: Failed to load clip at path {clipPath} with Addressables");
                        _failedPaths.Add(clipPath);
                        tcs.SetResult(null);
                    }
                    
                    _loadingTasks.Remove(clipPath);
                });
                
                return await tcs.Task;
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                _failedPaths.Add(clipPath);
                _loadingTasks.Remove(clipPath);
                return null;
            }
        }

        /// <summary>
        /// 预加载音频资源
        /// Preload audio clip
        /// </summary>
        /// <param name="clipPath">资源路径 Resource path</param>
        public static void PreloadClip(string clipPath)
        {
            if (string.IsNullOrEmpty(clipPath))
                return;
                
            if (!_clipCache.ContainsKey(clipPath) && !_loadingTasks.ContainsKey(clipPath) && !_failedPaths.Contains(clipPath))
            {
                LoadClipAsync(clipPath);
            }
        }

        /// <summary>
        /// 卸载音频资源
        /// Unload audio clip
        /// </summary>
        /// <param name="clipPath">资源路径 Resource path</param>
        public static void UnloadClip(string clipPath)
        {
            if (_clipCache.TryGetValue(clipPath, out AudioClip clip))
            {
                // 如果是Resources加载的资源，需要卸载
                if (clip != null && clipPath.StartsWith("Assets/") == false)
                {
                    Resources.UnloadAsset(clip);
                }
                
                // 如果是Addressable加载的资源，需要通知AddressableMgr卸载
                else if (clip != null)
                {
                    AddressableMgr.Inst.Unload(clipPath);
                }
                
                _clipCache.Remove(clipPath);
                _failedPaths.Remove(clipPath);
            }
        }

        /// <summary>
        /// 清空缓存
        /// Clear cache
        /// </summary>
        public static void ClearCache()
        {
            // 卸载Resources资源
            foreach (var entry in _clipCache)
            {
                string clipPath = entry.Key;
                AudioClip clip = entry.Value;
                
                if (clip != null && clipPath.StartsWith("Assets/") == false)
                {
                    Resources.UnloadAsset(clip);
                }
                else if (clip != null)
                {
                    AddressableMgr.Inst.Unload(clipPath);
                }
            }
            
            _clipCache.Clear();
            _loadingTasks.Clear();
            _failedPaths.Clear();
        }
    }
} 