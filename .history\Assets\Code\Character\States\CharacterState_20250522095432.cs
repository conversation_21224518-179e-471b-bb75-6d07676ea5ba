using UnityEngine;
using System.Collections;
using SubwayPvp.Core.Camera;
using RG.Cameras;
using RG.Inputs;

namespace RG.Characters.States
{
    /// <summary>
    /// 角色状态基类
    /// </summary>
    public abstract class CharacterState : MonoBehaviour
    {
        [SerializeField] protected CameraData _cameraData = null;
        [SerializeField] protected bool _hasEnded = false;

        protected Game _game;
        protected CharacterBase _character;
        protected CharacterCamera _characterCamera;
        protected CharacterRendering _characterRendering;
        protected CharacterController _characterController;

        protected virtual void Awake()
        {
            _game = Game.Instance;
            _character = _game.Character;
            _characterCamera = _character.CharacterCamera;
            _characterRendering = _character.CharacterRendering;
            _characterController = _character.CharacterController;
        }

        protected virtual void Reset()
        {
            _cameraData = new CameraData();
        }

        public CameraData CameraData { get { return _cameraData; } }

        public virtual void HandleSwipe(SwipeDir swipeDir) { }

        public virtual IEnumerator Begin()
        {
            yield break;
        }

        public virtual IEnumerator End()
        {
            yield break;
        }

        public virtual void HandleCriticalHit() { }

        public virtual void HandleDoubleTap() { }

        /// <summary>
        /// 进入状态
        /// </summary>
        public virtual void Enter(CharacterBase character)
        {
            _character = character;
        }

        /// <summary>
        /// 退出状态
        /// </summary>
        public virtual void Exit()
        {
        }

        /// <summary>
        /// 更新状态逻辑
        /// </summary>
        public virtual void Update()
        {
        }
        
        /// <summary>
        /// FixedUpdate 更新状态逻辑
        /// </summary>
        public virtual void FixedUpdate()
        {
        }
    }
}