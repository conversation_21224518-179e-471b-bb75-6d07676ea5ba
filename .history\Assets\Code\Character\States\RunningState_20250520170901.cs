using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class RunningState : CharacterState
    {
        [HideInInspector]
        public float cameraFOV = 40f;

        public bool transitionFromHeight = false;

        private float tunnelStartZ;
        private Curve offsetDeltaCurve = null;

        public const float CHARACTER_CHANGE_TRACK_LENGTH = 30;

        public const float DEFAULT_FOV = 40f;

        public RunPositions currentRunPosition;
        public enum RunPositions { ground, station, train, movingTrain, air };

        private Queue<Collider> GrindedTrains = new Queue<Collider>();
        private int GrindedTrainsBufferSize = 5;

        public event Action RunStarted = null;
        public event Action RunEnded = null;

        private static RunningState instance = null;
        protected override void Awake()
        {
            base.Awake();

            cameraFOV = DEFAULT_FOV;

            _character.OnStumble += (string stumbleAniType, StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, string colliderName, bool safeStumble) =>
            {
                _character.CharacterCamera.Shake();
            };

            _character.OnLanding += UpdateGroundTag;
        }

        private void _updateSpeed()
        {
            _character.SetCurrentSpeed(_game.Speed);
        }

        public override IEnumerator Begin()
        {
            RunStarted.SafeInvoke();

            _character.charSpeed.Begin();

            transitionFromHeight = false;
            bool transitionWithRoll = false;

            if (_character.GamePosition.y > 70f && _character.GamePosition.y < 125f)
            {
                transitionFromHeight = true;
            }

            _character.CharacterTrigger.enabled = true;

            _character.LastGroundedY = _character.GamePosition.y;

            var setAnimationSpeedEvent = new AnimationEvent();
            setAnimationSpeedEvent.functionName = "SetAnimationSpeedEvent";
            setAnimationSpeedEvent.time = 0.1f;
            setAnimationSpeedEvent.messageOptions = SendMessageOptions.RequireReceiver;

            while (true)
            {
                _character.runningTime += Time.deltaTime;

                this._updateSpeed();
                if (_character.isDropMoving)
                {
                    _character.MoveDrop();
                }
                else if (!_character.isStopRunning)
                {
                    _character.ApplyGravity();
                    _character.MoveForward();
                }

                if (transitionFromHeight)
                {
                    if (_character.IsGrounded.Value || _character.inAirJump && _characterController.isGrounded)
                    {
                        transitionFromHeight = false;

                        _character.ForceLeaveSubway();
                    }
                }

                _character.CheckInAirJump();

                UpdateInAirRunPosition();

                if (_character.GamePosition.z > PvpMgr.ROUTE_LENGTH)
                {
                    Character.Instance.charSpeed.ChangeState(CharacterSpeedState.Idle);
                    NetDirector.Rule_PostZ(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position);
                    NetDirector.PostRoom_EndGame(10, PhotonNetwork.player.ID, RG.XPluginAccount._userid, true);
                    Game.Instance.ChangeState(GameStateType.Die);
                    NetDirector.Post_GetRankIdx(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position.z, RG.XPluginAccount._userid);
                }
                else if (PvpMgr.Inst.IsEnterEndGame)
                {
                    if (PvpMgr.Inst.EnterEndGameTime - PhotonNetwork.time < 0)
                    {
                        Character.Instance.charSpeed.ChangeState(CharacterSpeedState.Idle);

                        Game.Instance.ChangeState(GameStateType.End);
                    }
                }
                yield return null;
            }
        }

        public override IEnumerator End()
        {
            RunEnded.SafeInvoke();

            yield break;
        }

        private void UpdateInAirRunPosition()
        {
            if (!_characterController.isGrounded)
                currentRunPosition = RunPositions.air;
        }

        private void LandedOnTrain(Collider trainCollider)
        {
            if (_character.BoardState.enabled)
            {
                if (!GrindedTrains.Contains(trainCollider))
                {
                    if (GrindedTrains.Count > GrindedTrainsBufferSize)
                        GrindedTrains.Dequeue();
                    GrindedTrains.Enqueue(trainCollider);
                }
            }
        }

        private void UpdateGroundTag(CharacterBase character)
        {
            var ray = new Ray(character.CharacterRoot.position, -Vector3.up);
            RaycastHit hitInfo;
            if (Physics.Raycast(ray, out hitInfo))
            {
                switch (hitInfo.collider.tag)
                {
                    case "Ground":
                        currentRunPosition = RunPositions.ground;
                        break;
                    case "HitTrain":
                        currentRunPosition = RunPositions.train;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "HitMovingTrain":
                        currentRunPosition = RunPositions.movingTrain;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "Station":
                        currentRunPosition = RunPositions.station;
                        break;
                    default:
                        break;
                }
            }
        }

        public override void HandleCriticalHit()
        {
            _character.CharacterCamera.Shake();
            if (_character.IsStumbling)
            {
                _character.StopStumble();
            }
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            if (Character.Instance.isStopRunning) return;
            if (PowerupMgr.Inst.IsInDevil)
            {
                switch (swipeDir)
                {
                    case SwipeDir.Up:
                        swipeDir = SwipeDir.Down;
                        break;
                    case SwipeDir.Down:
                        swipeDir = SwipeDir.Up;
                        break;
                    case SwipeDir.Left:
                        swipeDir = SwipeDir.Right;
                        break;
                    case SwipeDir.Right:
                        swipeDir = SwipeDir.Left;
                        break;
                    default:
                        break;
                }
            }

            float currentSpeed = _character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }
            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    if (_character.IsTeleboard)
                    {
                        _character.ChangeTrackBy(-1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        _character.ChangeTrackBy(-1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Right:
                    if (_character.IsTeleboard)
                    {
                        _character.ChangeTrackBy(+1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        _character.ChangeTrackBy(+1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Up:
                    Jump();
                    break;
                case SwipeDir.Down:

                    _character.Roll();

                    if (RouteManager.Instance.IsCharacterInAnyRoute)
                    {
                        Vector3 routeRelativeCharacterPosition = RouteManager.Instance.CharacterRoute.GetRelativePosition(_character.GamePosition);

                        if (routeRelativeCharacterPosition.y > 200)
                        {
                            RGLogger.LogWarning("Character", "Accellerated Fall");

                            _character.verticalSpeed = -_character.CalculateJumpVerticalSpeed(routeRelativeCharacterPosition.y);
                        }
                    }

                    break;
            }
        }

        private void Jump()
        {
            _character.Jump();
        }

        public static RunningState Instance
        {
            get
            {
                return instance ?? (instance = FindObjectOfType(typeof(RunningState)) as RunningState);
            }
        }
    }
}
