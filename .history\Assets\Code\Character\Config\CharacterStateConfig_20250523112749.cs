using UnityEngine;
using System;
using System.Collections.Generic;

namespace RG.Characters.Config
{
    /// <summary>
    /// 角色状态配置
    /// </summary>
    [CreateAssetMenu(fileName = "CharacterStateConfig", menuName = "Game/Character/Character State Config")]
    public class CharacterStateConfig : ScriptableObject
    {
        [Header("角色信息")]
        [Tooltip("角色ID")]
        public string CharacterId;

        [Serializable]
        public class StateTransitionConfig
        {
            [Tooltip("源状态")]
            public string FromState;
            
            [Tooltip("目标状态")]
            public string ToState;
            
            [Tooltip("转换条件")]
            public string Condition;
            
            [Tooltip("转换优先级")]
            public int Priority;
        }

        [Serializable]
        public class StateBehaviorConfig
        {
            [Tooltip("状态名称")]
            public string StateName;
            
            [Tooltip("状态动画名称")]
            public string AnimationName;
            
            [Tooltip("状态持续时间")]
            public float Duration;
            
            [Tooltip("是否可以被打断")]
            public bool CanBeInterrupted;
            
            [Tooltip("状态特效")]
            public string[] Effects;
        }

        [Header("状态转换配置")]
        [Tooltip("状态转换列表")]
        public List<StateTransitionConfig> StateTransitions = new List<StateTransitionConfig>();

        [Header("状态行为配置")]
        [Tooltip("状态行为列表")]
        public List<StateBehaviorConfig> StateBehaviors = new List<StateBehaviorConfig>();

        [Header("状态参数")]
        [Tooltip("地面检测高度")]
        public float GroundCheckHeight = 0.1f;
        
        [Tooltip("二段跳冷却时间")]
        public float DoubleJumpCooldown = 0.5f;
        
        [Tooltip("翻滚持续时间")]
        public float RollDuration = 0.5f;
        
        [Tooltip("滑行持续时间")]
        public float GlideDuration = 1.0f;
        
        [Header("状态事件")]
        [Tooltip("状态进入事件")]
        public string[] StateEnterEvents;
        
        [Tooltip("状态退出事件")]
        public string[] StateExitEvents;
        
        [Tooltip("状态更新事件")]
        public string[] StateUpdateEvents;

        /// <summary>
        /// 获取状态转换配置
        /// </summary>
        public StateTransitionConfig GetStateTransition(string fromState, string toState)
        {
            return StateTransitions.Find(t => 
                t.FromState == fromState && t.ToState == toState);
        }

        /// <summary>
        /// 获取状态行为配置
        /// </summary>
        public StateBehaviorConfig GetStateBehavior(string stateName)
        {
            return StateBehaviors.Find(b => b.StateName == stateName);
        }
    }
} 