{"activeFile": {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Route\\Builders\\Builders.cs"}, "files": [{"path": "c:\\Users\\<USER>\\.cursor\\mcp.json"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\.cursor\\rules\\resource-management-guide.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\.cursor\\rules\\code-style-guide.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\PUNNetwork\\IMService\\TkIMService.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\.history\\.cursor\\rules\\code-style-guide_20250424143727.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\.cursor\\rules\\unity-project-guide.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\.cursor\\rules\\refactor.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Lua\\Hotfix\\CodeSet.asset"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\cursor\\rules\\code-style-guide.mdc"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\MainScreen\\UIMainScreenHomeModelAnimation.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\MainScreen\\UIMainScreen.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\MainScreen\\MeView\\Board\\Upgrade\\UIMainScreenMeBoardUpgradeSucceedPopup.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\MainScreen\\MeView\\Character\\UIMainScreenMeCharacterView.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\Settings\\UISettingsScreen.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Localization\\LocalizationMgr.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Prefabs\\Login\\Login.prefab"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\Login\\LoginView.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\UI\\UIManager.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Network\\NetworkManager.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Component\\UI\\UIManager.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Lua\\Hotfix\\LuaEngine.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Lua\\Hotfix\\LuaCodeSets.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Lua\\LuaManager.cs"}, {"path": "g:\\subway\\new_pvp_client\\trunk\\unity\\Assets\\Code\\Common\\GameConfig.cs"}]}