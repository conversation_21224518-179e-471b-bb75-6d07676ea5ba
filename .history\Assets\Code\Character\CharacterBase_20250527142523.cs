using System;
using System.Collections;
using System.Collections.Generic;
using RG.Common;
using RG.Cameras;
using UnityEngine;
using RG.Routes;
using RG.Level;
using RG;
using CodeStage.AntiCheat.ObscuredTypes;
using SubwayPvp.Core.Audio;
using RG.Characters.States;
using RG.Characters.Components;
using RG.Common.Logger;

namespace RG.Characters
{
    /// <summary>
    //  处理角色的移动与碰撞
    /// </summary>
    public class CharacterBase : PvpLogicBehaviour
    {
        private ObscuredFloat _speed = 0;
        protected string _uid = null;
        public string UID => this._uid;

        #region Property


        [HideInInspector]
        public CapsuleCollider CharacterTrigger;
        public SuperSneakersJump? _superSneakersJump;
        public float CurrentSpeedNormalized { get { return CurrentSpeed / Game.Instance.SpeedInfo.min; } }
        public bool _startedJumpFromGround;
        public float _trainJumpSampleZ;
        public bool _trainJump;
        public float _glideTimeLeft;
        public bool _vTolEnded = false;
        public float verticalFallSpeedLimit = -1;
        [HideInInspector]
        public bool _willSecondJump = false;
        public float sameLaneTimeStamp = 0;
        [HideInInspector]
        public int _trackMovement = 0;
        [HideInInspector]
        public int _trackMovementNext = 0;
        [HideInInspector]
        public int _ignoredMovement = 0;
        [SerializeField]
        public float _glideTime = 0.1f;
        [SerializeField]
        public float superSneakersJumpApexRatio = 0.5f;
        public bool inAirJump = false;
        public float verticalSpeed = 0;
        public float jumpHeightNormal = 20;
        public float gliderGravity = 0.01f;
        [SerializeField]
        protected float _gravity = 200f;
        public bool _jumpUpperCollision = false;
        public float _jumpUpperCollisionColliderMinY = 0f;
        public bool _jumpUpperCollisionEvaded = true;
        [HideInInspector]
        public float SubwayMaxY;
        [HideInInspector]
        public bool StopColliding; //Used when moving character back to checkpoint
        [HideInInspector]
        public float JumpHeight;
        [HideInInspector]
        public float x = 0f;
        [HideInInspector]
        public int _initialTrackIndex = 1;
        [HideInInspector]
        public float _characterRotation = 0;
        [HideInInspector]
        public int _trackIndexTarget = 0;
        [HideInInspector]
        public float _trackIndexPosition = 0;
        public Vector3 GamePosition => this._currentDistance;
        public BoardState BoardState { get { return _boardState; } }
        private int _roadIndex;
        public int CurrentRoadIndex
        {
            get
            {
                return _roadIndex;
            }
            set
            {
                if (_roadIndex != value)
                {
                    _roadIndex = value;
                }
            }
        }
        private AnimationCurve _glideVerticalSpeed = new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1));
        private StumbleState _stumbleState = StumbleState.Standard;
        private Vector3 _currentDistance = Vector3.zero;
        private Vector3 _currentPosition = Vector3.zero;
        public Vector3 TransformPosition => this._currentPosition;
        private BoardState _boardState;
        private bool _isFalling = false;
        public bool IsFalling { get { return _isFalling; } set { _isFalling = value; } }
        private bool _isJumping = false;
        public bool IsJumping { get { return _isJumping; } set { _isJumping = value; } }
        private bool _isRolling = false;
        public bool IsRolling { get { return _isRolling; } set { _isRolling = value; } }
        private bool _isColliderCar;
        public bool IsColliderCar { get { return _isColliderCar; } set { _isColliderCar = value; } }
        public bool isStopRunning { get { return stopRunningFlag.Count > 0; } }
        public List<PowerupPropType> stopRunningFlag = new List<PowerupPropType>();
        public bool isDropMoving { get { return dropMovingFlag.Count > 0; } }
        public List<PowerupPropType> dropMovingFlag = new List<PowerupPropType>();
        public bool isPVPFinished { get { return GamePosition.z >= PvpMgr.ROUTE_LENGTH; } }
        public float SuperJumpAltitude = 40;
        public bool _movedSinceLastFixedUpdate = true;
        [HideInInspector]
        public float LastGroundedY;

        private ObscuredFloat _runningTime;
        public float runningTime { get { return _runningTime; } set { _runningTime = value; } }
        private CharacterController _characterController;
        public CharacterController CharacterController { get { return _characterController; } set { _characterController = value; } }
        private CharacterCamera _characterCamera;
        public CharacterCamera CharacterCamera { get { return _characterCamera; } set { _characterCamera = value; } }
        private CollectionEmptyNotifier _squeezeCollider = new CollectionEmptyNotifier();
        public CollectionEmptyNotifier SqueezeCollider { get { return _squeezeCollider; } set { _squeezeCollider = value; } }
        private CharacterModel _characterModel;
        public CharacterModel CharacterModel { get { return _characterModel; } set { _characterModel = value; } }
        private CharacterRendering _characterRendering;
        public CharacterRendering CharacterRendering { get { return _characterRendering; } }
        public Transform CharacterRoot { get { return _characterRoot; } set { _characterRoot = value; } }
        private Transform _characterRoot;
        public AnimationCurve superSneakersJumpCurve;
        public NetPlayerBehavior netPlayerBehavior { get { return _netPlayerBehavior; } }
        private NetPlayerBehavior _netPlayerBehavior;
        private float _superJumpOriginal;

        public int TrackMovement
        {
            get { return _trackMovement; }
        }
        public CollisionNotifier CharacterTriggerNotifier;
        public bool IsLowriderEnd = false;
        public bool IsDeath = false;
        protected Game _game;

        // 状态管理器
        private CharacterStateManager _stateManager;
        public CharacterStateManager CharacterStateManager => _stateManager;

        // 当前状态通过状态管理器获取
        public CharacterState CurrentState => _stateManager?.CurrentState;

        #region Notify Evens

        public delegate void OnChangeTrackDelegate(OnChangeTrackDirection direction);
        public event OnChangeTrackDelegate OnChangeTrack;
        public delegate void OnStumbleDelegate(string stumble, StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, string colliderName, bool safeStumble);
        public event OnStumbleDelegate OnStumble;
        public delegate void OnReviveDelegate();
        public event OnReviveDelegate OnRevive;
        public delegate void OnResetTeleportFXDelegate();
        public event OnResetTeleportFXDelegate OnResetTeleportFX;
        public delegate void OnHitByTrainDelegate();
        public event OnHitByTrainDelegate OnHitByTrain;
        public delegate void OnJumpDelegate(float jumpHeight, bool willPlaySound);
        public event OnJumpDelegate OnJump;
        public delegate void OnJumpIfHitByTrainDelegate();
        public event OnJumpIfHitByTrainDelegate OnJumpIfHitByTrain;
        public delegate void OnRollDelegate();
        public event OnRollDelegate OnRoll;
        public delegate void OnHangtimeDelegate();
        public event OnHangtimeDelegate OnHangtime;
        public ValueChangeNotifier<bool> IsGrounded = new ValueChangeNotifier<bool>(false);
        public delegate void OnTeleboardStartDelegate(float duration, Vector3 offset, float gameSpeed);
        public event OnTeleboardStartDelegate OnTeleboardStart;
        public System.Action<bool> OnRouteDone;
        public event System.Action<CharacterBase> OnLanding;
        public event System.Action<CharacterBase> OnLandingEnd;
        public event Action OnSurpass;
        public event Action OnBeSurpassed;
        public delegate void OnShowEffectSkillforUIDelegate(CharacterModel model, List<int> showEffect);
        public event OnShowEffectSkillforUIDelegate OnShowEffectSkillforUIEvent;

        public delegate void OnJetPackDelegate(bool opt);
        public event OnJetPackDelegate OnJetPack;

        #endregion

        #region Virtual Function
        public virtual int PlayerID { get { return 0; } }
        public virtual float CurrentSpeed => this._speed;
        public virtual bool IsJumpingHigher => (BoardState.isActive && BoardState.IsJumper);
        public virtual bool IsTeleboard => (BoardState.isActive && BoardState.IsTeleporter);
        public virtual bool IsGliderboard => (BoardState.isActive && BoardState.IsGlider);
        public virtual CharacterSpeedBase charSpeed { get { return null; } }
        public virtual IEnumerator ChangeTrackCoroutine(int move, float duration) { yield return null; }
        public virtual void NotifyOnRevive() { this.IsDeath = false; if (OnRevive != null) OnRevive(); }
        public virtual bool IsRunningOnGround() { return false; } // _runningState.currentRunPosition == RunningState.RunPositions.ground;
        public virtual bool IsRunningAir() { return false; }      // _runningState.currentRunPosition == RunningState.RunPositions.ground;

        public virtual void Awake()
        {
            // 延迟状态管理器初始化，确保所有依赖都已准备好
            // InitializeStateManager();
        }

        public virtual void Initialize()
        {
            if (_stateManager != null) return;
            
            InitializeStateManager();
            
            if (_stateManager == null)
            {
                Debug.LogError("[CharacterBase] Initialize: 状态管理器初始化失败");
                return;
            }
            
            // 初始化其他组件
            _characterController = GetComponent<CharacterController>();
            if (_characterController == null)
            {
                Debug.LogError("[CharacterBase] Initialize: 未找到 CharacterController 组件");
            }
            
            CharacterTrigger = GetComponent<CapsuleCollider>();
            if (CharacterTrigger == null)
            {
                Debug.LogError("[CharacterBase] Initialize: 未找到 CapsuleCollider 组件");
            }
            
            _boardState = new BoardState();
            _game = Game.Instance;
        }

        private void InitializeStateManager()
        {
            if (_stateManager != null) return;
            
            _stateManager = new CharacterStateManager(this);
            if (_stateManager == null)
            {
                Debug.LogError("[CharacterBase] InitializeStateManager: 创建状态管理器失败");
                return;
            }
            
            // 注册基本状态
            _stateManager.RegisterState<RunningState>();
            _stateManager.RegisterState<JumpingState>();
            _stateManager.RegisterState<RollingState>();
            
            // 设置初始状态
            _stateManager.ChangeState<RunningState>();
            
            // 确保所有状态都初始化
            _stateManager.InitAllStates();
        }

        private void Update()
        {
            _stateManager?.CurrentState?.OnStateUpdate();
        }

        private void FixedUpdate()
        {
            _stateManager?.CurrentState?.OnStateFixedUpdate();
        }

        //----------------------------------------------------------------------------
        // 	Public methods
        //----------------------------------------------------------------------------
        public virtual void ChangeTrackBy(int trackDelta, float duration)
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.ChangeTrackBy(trackDelta, duration);
        }

        public virtual void Roll()
        {
            if (IsRolling) return;

            _glideTimeLeft = _glideTime;

            if (_superSneakersJump != null)
                _superSneakersJump = null;

            SqueezeCollider.Add(this);
            verticalSpeed = CharacterConstant.ROLL_VERTICAL_SPEED;
            IsRolling = true;
            inAirJump = false;
            SendRollEvent();
        }

        public virtual void MoveForward()
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.MoveForward();
        }

        public virtual bool Jump()
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            return GetComponent<CharacterMoveComponent>()?.Jump() ?? false;
        }

        public virtual void ApplyGravity()
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.ApplyGravity();
        }

        public virtual float Gravity
        {
            get
            {
                return _gravity;
            }
        }
        #endregion


        public bool IsAboveGroundOfCharacterRoute
        {
            get
            {
                float yRelativeToRouteGround = TransformPosition.y;
                if (RouteManager.Instance.IsCharacterInAnyRoute)
                {
                    yRelativeToRouteGround = RouteManager.Instance.CharacterRoute.GetRelativePosition(TransformPosition).y;
                }

                return yRelativeToRouteGround > CharacterConstant.GROUND_CONSIDERED_HEIGHT;
            }
        }

        public StumbleState CurrentStumbleState
        {
            get
            {
                return _stumbleState;
            }
            set
            {
                _stumbleState = value;
            }
        }

        public void SetCurrentSpeed(float speed)
        {
            _speed = speed;
        }

        #region PhotonNetWork
        public void OpenPhotonNetWork(int _viewId, int _ownId)
        {
            _netPlayerBehavior = gameObject.AddComponent<NetPlayerBehavior>();
            _netPlayerBehavior.Init(_viewId, _ownId);
        }

        public void DestroyPhotonNetWork()
        {
            if (_netPlayerBehavior != null)
            {
                DestroyImmediate(_netPlayerBehavior);
                _netPlayerBehavior = null;
            }
        }

        public bool IsBreakLink()
        {
            return _netPlayerBehavior == null;
        }
        #endregion

        public float CalculateJumpVerticalSpeed(float jumpHeight)
        {
            return Mathf.Sqrt(2 * jumpHeight * Gravity);
        }

        public float GetJumpDistance(float speed, float jumpHeight)
        {
            return speed * 2f * CalculateJumpVerticalSpeed(jumpHeight) / Gravity;
        }

        public void ForceChangeTrack(int movement, float duration)
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.ForceChangeTrack(movement, duration);
        }

        public void InitialTrackIndex(int index)
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.InitialTrackIndex(index);
        }

        public void MoveDrop()
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.MoveDrop();
        }

        public void CheckInAirJump()
        {
            // TODO: 兼容性转发，后续彻底迁移到组件
            GetComponent<CharacterMoveComponent>()?.CheckInAirJump();
        }

        public void SetModelRotation(Quaternion rotation)
        {
            if (_characterModel == null)
            {
                Debug.LogError("[CharacterBase] SetModelRotation: _characterModel is null! 检查角色模型是否正确初始化。");
                return;
            }
            _characterModel.transform.localRotation = rotation;
        }
        public Quaternion GetModelRotation()
        {
            if (_characterModel == null)
            {
                Debug.LogError("[CharacterBase] GetModelRotation: _characterModel is null! 检查角色模型是否正确初始化。");
                return Quaternion.identity;
            }
            return _characterModel.transform.localRotation;
        }
        public void ResetModelRotation()
        {
            if (_characterModel == null)
            {
                Debug.LogError("[CharacterBase] ResetModelRotation: _characterModel is null! 检查角色模型是否正确初始化。");
                return;
            }
            _characterModel.transform.localRotation = Quaternion.identity;
        }

        public void SendShowSkillEffectEvent(CharacterModel model, List<int> showEffect)
        {
            if (OnShowEffectSkillforUIEvent != null)
            {
                OnShowEffectSkillforUIEvent(model, showEffect);
            }
        }

        public void SendJumpOverTrainEvent()
        {

        }

        public void SendTeleboardEvent(Vector3 offset, float duration, float speed)
        {
            if (OnTeleboardStart != null)
                OnTeleboardStart(duration, offset, speed);
        }

        public void SendHangtimeEvent()
        {
            if (OnHangtime != null) OnHangtime();
        }

        public void SendRollEvent()
        {
            if (this is Character)
            {
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.Roll);
            }
            if (OnRoll != null) OnRoll();
        }

        public void SendHitTrainEvent()
        {
            if (OnHitByTrain != null) OnHitByTrain();
        }

        public void SendChangeTrackEvent(OnChangeTrackDirection direction)
        {
            if (OnChangeTrack != null) OnChangeTrack(direction);
        }

        public void SendResetTeleportFXEvent()
        {
            if (OnResetTeleportFX != null)
                OnResetTeleportFX();
        }

        public void CriticalHitGeneratedEnvironment()
        {
            PFXSheld shield = GetComponentInChildren<PFXSheld>();
            if (shield != null)
            {
                shield.Defense();
            }
            //先设置掉落标记
            dropMovingFlag.Add(PowerupPropType.Empty);
            //再切换状态，控制滑板消失切换动作
            charSpeed.ChangeState(CharacterSpeedState.Bump, speedLossRatio: 100);
            Transform prefab = Resources.Load<Transform>($"{PowerupMgr.RES_PREFAB_POWERUP_RELATIVE_PATH}/Disappear");
            if (prefab != null)
            {
                Transform trans = Pooling.PoolManager.Spawn(prefab);
                trans.parent = transform;
                trans.localPosition = Vector3.zero;
                trans.localRotation = Quaternion.identity;
                trans.localScale = Vector3.one;
            }
        }

        public void NotifyOnStumble(string stumbleAniType, StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, string colliderName, bool safeStumble)
        {
            if (OnStumble != null) OnStumble(stumbleAniType, stumbleType, horizontalHit, verticalHit, colliderName, safeStumble);
        }

        public void NotifyJetPack(bool opt)
        {
            if (OnJetPack != null) OnJetPack(opt);
        }

        public void NotifyOnJump(float jumpHeight, bool willPlaySound)
        {
            if (IsJumpingHigher)
            {
                AudioManager.Instance.PlaySFX("audio_ability_higherJump");
            }
            if (OnJump != null) OnJump(jumpHeight, willPlaySound);
        }

        public void NotifyOnJumpIfHitByTrain()
        {
            if (OnJumpIfHitByTrain != null) OnJumpIfHitByTrain();
        }

        public void NotifySurpass()
        {
            if (this.OnSurpass != null)
            {
                this.OnSurpass.Invoke();
            }
            EventMgr.Instance.sendWithoutCache(EVENT_KEY.PVP_ABILITY_PRESS_ADVANTAGE, Character.Instance);
        }

        public void NotifyBeSurpassed()
        {
            if (this.OnBeSurpassed != null)
            {
                this.OnBeSurpassed.Invoke();
            }
        }

        public void SetGamePosition(Vector3 gamePosition)
        {
            transform.position = GamePositionRoot.Instance.GetWorldPosition(gamePosition);
            CachePositions();
        }

        public void CachePositions()
        {
            _currentPosition = transform.position;
            _currentDistance = GamePositionRoot.Instance.GetGamePosition(transform);
        }

        public void EndRoll()
        {
            if (this is Character)
            {
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.EndRoll);
            }

            ExpandColliders();
            IsRolling = false;
        }

        public void ExpandColliders()
        {
            if (CharacterController.enabled)
            {
                CharacterController.Move(Vector3.up * 2f);
            }

            SqueezeCollider.Remove(this);

            if (CharacterController.enabled)
            {
                CharacterController.Move(Vector3.down * 2f);
            }
            CachePositions();
        }

        public void NotifyOnLanding()
        {
            if (this is Character)
            {
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.Landing);
            }

            if (BoardState.IsLowrider && IsLowriderEnd)
            {
                SqueezeCollider.Remove(this);
                BoardState.IsLowrider = false;
                IsLowriderEnd = false;
            }

            if (OnLanding != null) OnLanding(this);
            if (OnLandingEnd != null) OnLandingEnd(this);
        }
        #endregion

        private void _registerEvent()
        {
        }

        /// <summary>
        /// 角色死亡时的处理逻辑
        /// Handle character death logic
        /// </summary>
        public void HandleOnDeath()
        {
            this.IsDeath = true;
        }

        public virtual void ForceLeaveSubway()
        {
            // 默认实现为空
        }

        public virtual bool IsStumbling
        {
            get { return false; }
        }

        public virtual void StopStumble()
        {
            // 默认实现为空
        }

        // 进入跑酷时初始化所有状态
        public void OnEnterGame()
        {
            CharacterStateManager?.InitAllStates();
            // TODO: 其他进入游戏初始化逻辑
        }

        // 退出一局时清理所有状态
        public void OnExitGame()
        {
            CharacterStateManager?.ClearAllStates();
            // TODO: 其他退出游戏清理逻辑
        }

        public T GetState<T>() where T : CharacterState
        {
            return CharacterStateManager != null ? CharacterStateManager.GetState<T>() : null;
        }
    }
}