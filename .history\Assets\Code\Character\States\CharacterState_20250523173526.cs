using UnityEngine;
using System.Collections;
using SubwayPvp.Core.Camera;
using RG.Cameras;
using RG.Inputs;
using RG.Characters;

namespace RG
{
    /// <summary>
    /// 角色状态基类
    /// </summary>
    public abstract class CharacterState : PvpLogicBehaviour
    {
        [SerializeField] protected CameraData _cameraData = null;
        [SerializeField] protected bool _hasEnded = false;

        protected Game _game;
        protected Character _character;
        protected CharacterCamera _characterCamera;
        protected CharacterRendering _characterRendering;
        protected CharacterController _characterController;

        protected CharacterBase Character { get; private set; }

        protected virtual void Awake()
        {
            _game = Game.Instance;
            _character = _game.Character;
            _characterCamera = _character.CharacterCamera;
            _characterRendering = _character.CharacterRendering;
            _characterController = _character.CharacterController;
        }

        protected virtual void Reset()
        {
            _cameraData = new CameraData();
        }

        public CameraData CameraData { get { return _cameraData; } }

        public virtual void HandleSwipe(SwipeDir swipeDir) { }

        public virtual IEnumerator Begin()
        {
            yield break;
        }

        public virtual IEnumerator End()
        {
            yield break;
        }

        public virtual void HandleCriticalHit() { }

        public virtual void HandleDoubleTap() { }

        public virtual void Initialize(CharacterBase character)
        {
            Character = character;
        }

        /// <summary>
        /// 进入状态时调用
        /// </summary>
        public virtual void Enter() { }

        /// <summary>
        /// 退出状态时调用
        /// </summary>
        public virtual void Exit() { }

        /// <summary>
        /// 状态逻辑更新
        /// </summary>
        public virtual void OnStateUpdate() { }

        /// <summary>
        /// 状态物理更新
        /// </summary>
        public virtual void OnStateFixedUpdate() { }

        /// <summary>
        /// 是否可以切换到目标状态
        /// </summary>
        public virtual bool CanTransitionTo(CharacterState nextState)
        {
            return true;
        }
    }
}