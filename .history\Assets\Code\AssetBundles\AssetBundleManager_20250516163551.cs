﻿using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using RG.Common;
using RG;
using RG.Common;
using Debug = UnityEngine.Debug;
using RG.Common.Logger;

public class AssetBundleManager
{
    public const string COMPRESSION_SIGNATURE = "subway_bundles";
    private const string ASSET_BUNDLE_RESOURCES_PATH = "AssetBundles";
    private const string DEBUG_TAG = "[AssetBundleManager]: ";
    private const char VALUE_SEPARATOR = '-';
    private const char ITEM_SEPARATOR = ',';

    private static string _assetBundleBaseServerUrl = "http://ssc-prod-cdn.ldoverseas.com/Bundles/";

    private static string assetBundleBaseServerUrl
    {
        get
        {
            string env = "DEV/";
            if (-1 == RG.Common.GameConfig.Env)
            {
                env = "PRE/";
            }
            else if (1 == RG.Common.GameConfig.Env)
            {
                env = "PRO/";
            }
            return _assetBundleBaseServerUrl + env;
        }
    }

    private static string _serverDirIos = "ios/";
    private static string _serverDirAndroid = "android";

    private static float _downloadAttemptRestTime = 10f;
    private static string _assetBundleExtension = ".bytes";

    private AssetBundleVersionControl _versionControl;

    //Bundles loading/unloading
    private List<string> _assetBundlesLoading = new List<string>();
    private Dictionary<string, AssetBundle> _loadedAssetBundles = new Dictionary<string, AssetBundle>();
    private Dictionary<string, bool> _pendingUnloads = new Dictionary<string, bool>();

    //Bundles download
    private Dictionary<string, int> _pendingDownloads;
    private bool _isDownloading = false;
    private float _lastInitializedDownloads = -_downloadAttemptRestTime;
    private bool _downloadableBundlesUpdated = false;
    private IEnumerator _currentDownloaderCoroutine;

    private bool IsDownloading
    {
        get
        {
            return _isDownloading;
        }
        set
        {
            _isDownloading = value;
            if (!value) StartDownloadIfPossible();
        }
    }

    #region Singleton
    private static AssetBundleManager _instance;

    private AssetBundleManager()
    {
        AssetBundleCache.Initialize();
        _versionControl = AssetBundleVersionControl.Instance;
        _versionControl.OnDownloadableAssetBundlesUpdated += OnDownloadableAssetBundlesUpdated;
        Game.OnStartGame += StopDownloading;
        Game.OnMenu += StartDownloadIfPossible;
        InitializeDownloadRequests();
    }

    public static AssetBundleManager Instance
    {
        get { return _instance ?? (_instance = new AssetBundleManager()); }
        private set { }
    }
    #endregion

    #region URL Handling
    private string GetAssetBundlesServerURL(string bundleName)
    {
        var directory = Application.version + "/" + "v" + _versionControl.GetCurrentlyAvailableBundleVersion() + "/";

        switch (Application.platform)
        {
            case RuntimePlatform.Android:
                directory += _serverDirAndroid;
                break;
            case RuntimePlatform.IPhonePlayer:
                directory += _serverDirIos;
                break;
            case RuntimePlatform.OSXEditor:
                directory += _serverDirIos;
                break;
            case RuntimePlatform.WindowsEditor:
                directory += _serverDirAndroid;
                break;
        }
        if (String.IsNullOrEmpty(directory)) RGLogger.LogError("AssetBundle", "No Asset Bundle directory found for platform: " + Application.platform);
        var serverDirectory = Path.Combine(assetBundleBaseServerUrl, directory);
        //The format on the CDN doesn't allow for capital letters, and we also need to add the .bytes extension if we are using it on the server too (done for practical reasons)
        return Path.Combine(serverDirectory, bundleName.ToLower() + _assetBundleExtension);

    }
    #endregion

    public AssetBundle GetAssetBundle(string bundleName)
    {
        RGLogger.Log("AssetBundle", "Bundle requested: " + bundleName);
        //If it's loaded, we fetch it
        AssetBundle requestedBundle = null;
        if (_loadedAssetBundles.TryGetValue(bundleName, out requestedBundle))
        {
            RGLogger.Log("AssetBundle", "Bundle is already loaded, fetching it: " + bundleName);
            return requestedBundle;
        }
        else
        {
            RGLogger.Log("AssetBundle", "It's not already loaded: " + bundleName);
            //If it's not, we check if we are already trying to load it
            if (!_assetBundlesLoading.Contains(bundleName))
            {
                RGLogger.Log("AssetBundle", "Attempt to load the bundle: " + bundleName);
                //We are not trying to load it, let's start
                _assetBundlesLoading.Add(bundleName);
                try
                {
                    return RequestAssetBundle(bundleName);
                }
                catch (Exception e)
                {
                    RGLogger.LogError("AssetBundle", "Exception caught while trying to retrieve an AssetBundle: " + bundleName + "Error: " + e);
                }
            }
        }
        return null;
    }

    private AssetBundle RequestAssetBundle(string bundleName)
    {
        AssetBundle assetBundle = null;

        int latestVersion = _versionControl.GetLatestVersionForBundle(bundleName);
        int cachedVersion = AssetBundleCache.GetVersionForCachedBundle(bundleName);

        if (latestVersion < 0)
        {
            RGLogger.LogError("AssetBundle", "No version information exists for bundle :" + bundleName);
        }
        else
        {
            //Compare with cached and see if we need to download
            if ((latestVersion > cachedVersion) && !_versionControl.IsBundleVersionShipped(bundleName, latestVersion))
            {
                //Make sure it's in the download request queue
                AddAssetBundleDownloadRequest(bundleName, latestVersion);
            }
        }

        //If there is no cached version
        if (cachedVersion < 0 || latestVersion > cachedVersion)
        {
            int shippedVersion = _versionControl.GetAvailableShippedVersionForBundle(bundleName);
            if (shippedVersion >= 0)
            {
                CacheAssetBundle(bundleName, shippedVersion);
                cachedVersion = AssetBundleCache.GetVersionForCachedBundle(bundleName);
            }
        }

        //We load whatever cached version we can get
        if (cachedVersion >= 0) assetBundle = AssetBundleCache.LoadAssetBundle(bundleName, cachedVersion);

        if (assetBundle != null)
        {
            if (_loadedAssetBundles.ContainsKey(bundleName))
            {
                _loadedAssetBundles[bundleName] = assetBundle;
            }
            else
            {
                _loadedAssetBundles.Add(bundleName, assetBundle);
            }
        }
        else
        {
            RGLogger.LogError("AssetBundle", "AssetBundle request finished unsuccessfully for bundle: " + bundleName);
        }

        //Remove the bundle name from the "loading" list
        if (_assetBundlesLoading.Contains(bundleName))
        {
            _assetBundlesLoading.Remove(bundleName);
        }
        return assetBundle;
    }

    private bool CacheAssetBundle(string bundleName, int version, byte[] bundleBytes = null)
    {
        if (bundleBytes != null)
        {
            return AssetBundleCache.CacheAssetBundle(bundleName, version, bundleBytes);
        }

        TextAsset bundleTextBytes = Resources.Load<TextAsset>(Path.Combine(ASSET_BUNDLE_RESOURCES_PATH, bundleName));
        if (bundleTextBytes)
        {
            var decompressedBundle = bundleTextBytes.bytes.Decompress(COMPRESSION_SIGNATURE);
            return AssetBundleCache.CacheAssetBundle(bundleName, version, decompressedBundle);
        }
        return false;
    }

    public void UnloadAssetBundle(string bundleName, bool unloadAllLoadedObjects)
    {
        if (_pendingUnloads.ContainsKey(bundleName))
        {
            _pendingUnloads[bundleName] = unloadAllLoadedObjects;
        }
        else
        {
            _pendingUnloads.Add(bundleName, unloadAllLoadedObjects);
        }
        CoroutineUtil.StartCoroutine(UnloadBundlesCoroutine());
    }

    private IEnumerator UnloadBundlesCoroutine()
    {
        yield return new WaitForEndOfFrame();
        //We attempt to unload the bundles that are no longer in use
        if (_pendingUnloads.Count <= 0) yield break;

        List<string> successfullyUnloadedBundles = new List<string>();

        foreach (KeyValuePair<string, bool> bundleToUnload in _pendingUnloads)
        {
            var bundleName = bundleToUnload.Key;
            var unloadAllLoadedObjects = bundleToUnload.Value;

            if (!_assetBundlesLoading.Contains(bundleName))
            {
                if (_loadedAssetBundles.ContainsKey(bundleName) && _loadedAssetBundles[bundleName] != null)
                {
                    _loadedAssetBundles[bundleName].Unload(unloadAllLoadedObjects);
                    _loadedAssetBundles.Remove(bundleName);
                }
                successfullyUnloadedBundles.Add(bundleName);
            }
        }

        foreach (string unloadedBundle in successfullyUnloadedBundles)
        {
            _pendingUnloads.Remove(unloadedBundle);
        }
    }

    #region BundleDownloads
    private void InitializeDownloadRequests()
    {
        _lastInitializedDownloads = Time.timeSinceLevelLoad;
        _pendingDownloads = new Dictionary<string, int>();

        _downloadableBundlesUpdated = false;
    }

    private void OnDownloadableAssetBundlesUpdated()
    {
        _downloadableBundlesUpdated = true;
    }

    private void AddAssetBundleDownloadRequest(string bundleName, int version)
    {
        if (_pendingDownloads.ContainsKey(bundleName))
        {
            if (_pendingDownloads[bundleName] <= version) return;
            _pendingDownloads[bundleName] = version;
        }
        else
        {
            _pendingDownloads.Add(bundleName, version);
        }
    }

    public void StartDownloadIfPossible()
    {
        if (_pendingDownloads == null || !_pendingDownloads.Any() || _downloadableBundlesUpdated)
        {
            if (AllowedToInitializeDownloads())
            {
                //Double check for failed downloads and possible updates
                InitializeDownloadRequests();
                if (_pendingDownloads.Any())
                {
                    StartDownloadIfPossible();
                }
            }
        }
        else
        {
            if (!IsDownloading && !Game.Instance.IsInGame.Value)
            {
                var bundle = _pendingDownloads.First();

                _currentDownloaderCoroutine = DownloadAssetBundle(bundle.Key, bundle.Value);

                CoroutineUtil.StartCoroutine(_currentDownloaderCoroutine);

            }
        }
    }

    private bool AllowedToInitializeDownloads()
    {
        return (_lastInitializedDownloads + _downloadAttemptRestTime) < Time.timeSinceLevelLoad;
    }

    private void StopDownloading()
    {
        if (!IsDownloading) return;
        CoroutineUtil.StopCoroutine(_currentDownloaderCoroutine);
        IsDownloading = false;
    }

    private IEnumerator DownloadAssetBundle(string bundleName, int version)
    {
        IsDownloading = true;
        bool downloadSuccessful = false;
        var serverUrl = GetAssetBundlesServerURL(bundleName);

        WWW www = new WWW(serverUrl);
        yield return www;
        int tryTime = 0;
        while (!www.isDone)
        {

            yield return new WaitForSeconds(10f);
            tryTime++;
            if (tryTime >= 20)
            {
                RGLogger.LogError("AssetBundle", "DownloadAssetBundleIos---------- timeout bundleName:" + bundleName);
                break;
            }
        }

        if (www.error != null)
        {
            downloadSuccessful = false;
            RGLogger.LogError("AssetBundle", "DownloadAssetBundleIos download failed bundleName: " + bundleName + "url " + serverUrl + " " + www.error);
        }
        else
        {
            try
            {
                var decompressedBundleBytes = www.bytes.Decompress(COMPRESSION_SIGNATURE);
                if (CacheAssetBundle(bundleName, version, decompressedBundleBytes))
                {
                    downloadSuccessful = true;
                    ResolveDownloadRequest(bundleName, version);
                }
                else
                {
                    RGLogger.LogError("AssetBundle", "Attempt to cache downloaded bundle from the server failed.");
                }
            }
            catch (Exception e)
            {
                RGLogger.LogError("AssetBundle", "Attempt to cache downloaded bundle from the server failed. error:" + e.Message);
            }
        }

        if (!downloadSuccessful) DownloadFailedFor(bundleName, version);
        IsDownloading = false;
        www.Dispose();
    }

    private void DownloadFailedFor(string bundleName, int version)
    {
        if (_pendingDownloads.ContainsKey(bundleName))
        {
            //We remove it; it will be added again when list is re-initialized
            _pendingDownloads.Remove(bundleName);
        }
        else
        {
            RGLogger.LogError("AssetBundle", "No pending download found for failed download: " + bundleName + " version " + version);
        }
    }

    private void ResolveDownloadRequest(string bundleName, int version)
    {
        if (_pendingDownloads.ContainsKey(bundleName))
        {
            if (_pendingDownloads[bundleName] <= version)
            {
                _pendingDownloads.Remove(bundleName);
            }
        }
    }
    #endregion

    #region Serialization

    public static string SerializeAssetBundleVersionData(Dictionary<string, int> versionData)
    {
        string serializedData = "";
        if (versionData != null)
        {
            foreach (KeyValuePair<string, int> kvp in versionData)
            {
                serializedData += kvp.Key + VALUE_SEPARATOR + kvp.Value + ITEM_SEPARATOR;
            }
        }
        return serializedData;
    }

    public static Dictionary<string, int> ParseAssetBundleVersionData(string serializedData)
    {
        var versionData = new Dictionary<string, int>();

        string[] items = serializedData.Split(ITEM_SEPARATOR);

        foreach (string item in items)
        {
            string[] values = item.Split(VALUE_SEPARATOR);
            if (values.Length != 2) continue;
            versionData.Add(values[0], Int32.Parse(values[1]));
        }
        return versionData;
    }
    #endregion
}
