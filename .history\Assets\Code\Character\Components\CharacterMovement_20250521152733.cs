using UnityEngine;
using System.Collections;
using RG.Characters;

namespace Assets.Code.Character.Components
{
    public class CharacterMovement : MonoBehaviour
    {
        private CharacterBase _character;
        private CharacterController _controller;
        private float _verticalSpeed;
        private bool _movedSinceLastFixedUpdate;
        private float _lastGroundedY;

        public void Initialize(CharacterBase character)
        {
            _character = character;
            _controller = character.CharacterController;
        }

        public void MoveForward()
        {
            Vector3 gamePosition = _character.GamePosition;
            float nextGamePositionZ = gamePosition.z + _character.CurrentSpeed * Time.deltaTime;
            Vector3 verticalMove = Vector3.zero;
            bool canMoveUp = true;

            if (_character.IsJumping && _character.JumpUpperCollision)
            {
                canMoveUp = false;
                if (!_character.JumpUpperCollisionEvaded && _character.JumpUpperCollisionColliderMinY < _character.CharacterTrigger.bounds.max.y)
                {
                    if (0f <= _verticalSpeed)
                        verticalMove = -_character.Gravity * Time.deltaTime * Time.deltaTime * Vector3.up;
                }
                else
                {
                    verticalMove = Vector3.zero;
                    _character.JumpUpperCollisionEvaded = true;
                }
            }

            if (_verticalSpeed < 0f || canMoveUp)
                verticalMove = _verticalSpeed * Time.deltaTime * Vector3.up;

            if (_character.IsJumping && _character.SuperSneakersJump.HasValue)
            {
                var jump = _character.SuperSneakersJump.Value;

                if (gamePosition.z < jump.EndGamePositionZ)
                {
                    if (canMoveUp)
                    {
                        float y_new = _character.SuperSneakersJumpCurve.Evaluate((nextGamePositionZ - jump.StartGamePositionZ) / jump.Length) * _character.SuperJumpAltitude + jump.StartGamePositionY;
                        float y_delta = y_new - gamePosition.y;
                        verticalMove = Vector3.up * y_delta;
                    }
                    else
                        verticalMove = Vector3.zero;
                }
                else
                {
                    _character.SuperSneakersJump = null;
                    _verticalSpeed = 0f;
                    verticalMove = Vector3.zero;
                }
            }

            Vector3 xzPositionTarget = _character.Route.GetPosition(_character.X, nextGamePositionZ);
            Vector3 xzPosition = new Vector3(gamePosition.x, 0f, gamePosition.z);
            Vector3 xzMove = xzPositionTarget - xzPosition;

            if (_controller.enabled)
            {
                _controller.Move(verticalMove + xzMove);
            }
            else
            {
                transform.position = _character.TransformPosition + xzMove;
            }

            _character.CachePositions();

            if (_controller.isGrounded)
            {
                _lastGroundedY = gamePosition.y;
            }
            _movedSinceLastFixedUpdate = true;
        }

        public void ApplyGravity()
        {
            if (_verticalSpeed < 0f && _controller.isGrounded)
            {
                if (_character.StartedJumpFromGround && _character.TrainJump && _character.IsRunningOnGround())
                {
                    _character.SendJumpOverTrainEvent();
                }

                if (!_character.IsRunningAir())
                {
                    _character.StartedJumpFromGround = false;
                }

                _character.IsGrounded.Value = true;

                if (_character.IsJumping || _character.IsFalling)
                {
                    _character.JumpUpperCollision = false;
                    _character.JumpUpperCollisionEvaded = true;

                    _character.IsJumping = false;
                    _character.IsFalling = false;
                    _character.NotifyOnLanding();
                }

                _verticalSpeed = 0f;
            }
            else
            {
                if (_character.StartedJumpFromGround && _character.TrainJumpSampleZ < _character.GamePosition.z)
                {
                    RaycastHit hit;
                    if (Physics.Raycast(new Ray(_character.TransformPosition, -Vector3.up), out hit))
                    {
                        if (hit.collider.CompareTag("HitMovingTrain") ||
                            hit.collider.CompareTag("HitTrain"))
                        {
                            _character.TrainJump = true;
                        }
                    }
                    _character.TrainJumpSampleZ += _character.CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }

            if (_character.IsFalling && _character.IsGliderboard)
            {
                if (_character.GlideTimeLeft < _character.GlideTime)
                {
                    _verticalSpeed = _character.GliderGravity * _character.GlideVerticalSpeed.Evaluate(_character.GlideTimeLeft / _character.GlideTime);
                    _character.GlideTimeLeft += Time.deltaTime;
                }
                else
                {
                    _verticalSpeed -= _character.GliderGravity * Time.deltaTime;
                }
            }
            else
            {
                _verticalSpeed -= _character.Gravity * Time.deltaTime;
            }

            if (!_controller.isGrounded)
            {
                if (!_character.IsFalling && _verticalSpeed < _character.VerticalFallSpeedLimit && !_character.IsRolling)
                {
                    _character.IsFalling = true;
                    _character.SendHangtimeEvent();
                    _character.IsGrounded.Value = false;
                }
            }
        }

        public float VerticalSpeed
        {
            get { return _verticalSpeed; }
            set { _verticalSpeed = value; }
        }

        public float LastGroundedY
        {
            get { return _lastGroundedY; }
            set { _lastGroundedY = value; }
        }

        public bool MovedSinceLastFixedUpdate
        {
            get { return _movedSinceLastFixedUpdate; }
            set { _movedSinceLastFixedUpdate = value; }
        }
    }
} 