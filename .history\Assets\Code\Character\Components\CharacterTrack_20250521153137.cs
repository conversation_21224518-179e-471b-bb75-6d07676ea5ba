using UnityEngine;
using System.Collections;
using RG.Characters;
using RG.Routes;

namespace Assets.Code.Character.Components
{
    public class CharacterTrack : MonoBehaviour
    {
        private CharacterBase _character;
        private int _initialTrackIndex;
        private int _currentTrackIndex;
        private int _trackIndexTarget;
        private float _sameLaneTimeStamp;
        private int _trackMovement;
        private int _trackMovementNext;
        private Coroutine _changeTrackCoroutine;

        public void Initialize(CharacterBase character)
        {
            _character = character;
        }

        public void InitialTrackIndex(int index)
        {
            _initialTrackIndex = index;
            _currentTrackIndex = _initialTrackIndex;
            _trackIndexTarget = _initialTrackIndex;
            float x = _character.Route.GetTrackX(_currentTrackIndex);
            Vector3 xPositionTarget = _character.Route.GetPosition(x, 0);
            _character.CharacterController.Move(xPositionTarget);
            _character.CachePositions();
        }

        public void ChangeTrack(int trackDelta, float duration)
        {
            _sameLaneTimeStamp = Time.time;

            if (_trackMovement != trackDelta)
            {
                ForceChangeTrack(trackDelta, duration);

                if (_character.IsTeleboard)
                {
                    float tSpeed = _character.CurrentSpeed;
                    if (_character.CurrentSpeed < RouteConstants.WIDTH_PER_LANE)
                    {
                        tSpeed = RouteConstants.WIDTH_PER_LANE;
                    }

                    if (trackDelta == 1)
                    {
                        float durationex = 17f / tSpeed;
                        _character.SendTeleboardEvent(new Vector3(20.0f, 0.0f, 1.0f), durationex, _character.CurrentSpeed);
                    }
                    else if (trackDelta == -1)
                    {
                        float durationex = 17f / tSpeed;
                        _character.SendTeleboardEvent(new Vector3(-20.0f, 0.0f, 1.0f), durationex, _character.CurrentSpeed);
                    }
                }
            }
            else
            {
                _trackMovementNext = trackDelta;
            }
        }

        private void ForceChangeTrack(int trackDelta, float duration)
        {
            _trackMovement = trackDelta;
            _trackIndexTarget = _currentTrackIndex + trackDelta;

            if (_changeTrackCoroutine != null)
            {
                StopCoroutine(_changeTrackCoroutine);
            }
            _changeTrackCoroutine = StartCoroutine(ChangeTrackCoroutine(duration));
        }

        private IEnumerator ChangeTrackCoroutine(float duration)
        {
            float startTime = Time.time;
            float startX = _character.X;
            float targetX = _character.Route.GetTrackX(_trackIndexTarget);

            while (Time.time < startTime + duration)
            {
                float t = (Time.time - startTime) / duration;
                float currentX = Mathf.Lerp(startX, targetX, t);
                Vector3 position = _character.Route.GetPosition(currentX, _character.GamePosition.z);
                _character.CharacterController.Move(position - _character.TransformPosition);
                _character.CachePositions();
                yield return null;
            }

            _currentTrackIndex = _trackIndexTarget;
            _trackMovement = 0;
            _changeTrackCoroutine = null;
        }

        public int CurrentTrackIndex
        {
            get { return _currentTrackIndex; }
        }

        public int TrackIndexTarget
        {
            get { return _trackIndexTarget; }
        }

        public float SameLaneTimeStamp
        {
            get { return _sameLaneTimeStamp; }
        }

        public int TrackMovement
        {
            get { return _trackMovement; }
        }

        public int TrackMovementNext
        {
            get { return _trackMovementNext; }
            set { _trackMovementNext = value; }
        }
    }
} 