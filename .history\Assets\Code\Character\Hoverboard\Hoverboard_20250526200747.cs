using UnityEngine;
using System.Collections.Generic;
using System;
using RG.Characters;
using RG;
using SubwayPvp.Core.Audio;

public class Hoverboard : MonoBehaviour
{
    private static Hoverboard instance = null;
    public static Hoverboard Instance
    {
        get
        {
            return instance ?? (instance = FindObjectOfType(typeof(Hoverboard)) as Hoverboard);
        }
    }

    // 初始化状态
    private bool _isInitialized = false;
    public bool IsInitialized { get { return _isInitialized; } }
    public event System.Action OnInitialized;

    public HoverboardSelection[] hoverboardSelector;
    public AudioClipInfo powerDownSound;
    public AudioClipInfo StartSound;

    private EnergyMagnet energyMagnet;
    private Character character;

    #region Events

    public delegate void OnSpeedStartDelegate();
    public event OnSpeedStartDelegate OnSpeedStart;

    public delegate void OnSpeedEndDelegate();
    public event OnSpeedEndDelegate OnSpeedEnd;
    #endregion

    private Dictionary<string, HoverboardSelection> hoverboardThatMatchBoardType = new Dictionary<string, HoverboardSelection>();
    private Dictionary<string, HoverboardSelection.CustomSet[]> BoardtypeCustomSets = new Dictionary<string, HoverboardSelection.CustomSet[]>();
    private Dictionary<BoardAbility, HoverboardSelection.CustomSet> AbilityTypeCustomSets = new Dictionary<BoardAbility, HoverboardSelection.CustomSet>();

    private GameObject _goBoard = null;

    public void Awake()
    {
        instance = this;
    }

    public void intialize()
    {
        character = Character.Instance;


        energyMagnet = gameObject.GetComponent<EnergyMagnet>();

        foreach (HoverboardSelection selectedHoverboard in hoverboardSelector)
        {
            if (selectedHoverboard?.customSets != null)
            {
                for (int i = 0; i < selectedHoverboard.customSets.Length; i++)
                {
                    var cs = selectedHoverboard.customSets[i];
                    if (cs != null && !this.AbilityTypeCustomSets.ContainsKey(cs.ability))
                    {
                        this.AbilityTypeCustomSets.Add(cs.ability, cs);
                    }
                }
            }

            if (!hoverboardThatMatchBoardType.ContainsKey(selectedHoverboard.boardItemId))
            {
                hoverboardThatMatchBoardType.Add(selectedHoverboard.boardItemId, selectedHoverboard);
                BoardtypeCustomSets.Add(selectedHoverboard.boardItemId, selectedHoverboard.customSets);
            }
            else
                throw new Exception("There are more hoverboards assigned to the hoverboard selection id=" + selectedHoverboard.boardItemId);
        }
    }

    public List<HoverboardSelection.CustomSet> GetActiveCustomSets(RG.Board.Board board)
    {
        List<HoverboardSelection.CustomSet> rst = null;
        if (board != null && board.CurrentAbilities != null)
        {
            rst = new List<HoverboardSelection.CustomSet>();
            foreach (var item in board.CurrentAbilities)
            {
                if (item.Value.Type != BoardAbility.None)
                {
                    HoverboardSelection.CustomSet cs = null;

                    if (!this.AbilityTypeCustomSets.TryGetValue(item.Value.Type, out cs))
                    {
                        //新增加技能需要入库....
                        cs = new HoverboardSelection.CustomSet() { ability = item.Value.Type };
                    }
                    cs.AbilityBean = item.Value;
                    rst.Add(cs);
                }
            }
        }
        return rst;
    }

    public void ShowBoardEff(GameObject newBoard, CharacterBase characterbase, RG.Board.Board board, bool isMenuEff = false)
    {
        this.ShowBoardEff(newBoard, characterbase.CharacterModel, board, isMenuEff);
    }

    public void ShowBoardEff(GameObject newBoard, CharacterModel characterModel, RG.Board.Board board, bool isMenuEff = false)
    {
        this.BoardtypeCustomSets.TryGetValue(board.itemsId, out HoverboardSelection.CustomSet[] customSets);
        if (customSets != null && customSets.Length > 0)
        {
            var normalSet = customSets[0];
            GameObject prefab = normalSet.VanityFxPrefab;
            if (isMenuEff)
            {
                prefab = normalSet.VanityMenuFxPrefab;
            }
            if (normalSet != null && prefab)
            {
                var noramlFxPrefab = prefab;
                if (characterModel.CurVanityFxPrefab != noramlFxPrefab)
                {
                    characterModel.AddCustomHoverboardModel(noramlFxPrefab, false, newBoard.transform, true);
                    characterModel.CurVanityFxPrefab = noramlFxPrefab;
                }
            }
        }
    }

    public BoardAbility[] SetupAbility(GameObject newBoard, CharacterBase characterbase, RG.Board.Board board)
    {
        //////////INITIALIZATIONS//////////
        Renderer[] baseModelRenderes = newBoard.GetComponentsInChildren<Renderer>();
        List<HoverboardSelection.CustomSet> customSets = null;
        BoardState boardState = characterbase.BoardState;
        var currentPlayer = characterbase is Character;

        List<BoardAbility> abilities = new List<BoardAbility>();

        var boardId = board.itemsId;

        customSets = GetActiveCustomSets(board);

        characterbase.BoardState.CustomSets = customSets;
        if (customSets != null && customSets.Count > 0)
        {
            Renderer lastBaseModel = null;
            for (int i = 0; i < customSets.Count; i++)
            {
                abilities.Add(customSets[i].ability);

                if (customSets[i].baseModelPrefab != null)
                {
                    Renderer curRenderer = characterbase.CharacterModel.AddCustomHoverboardModel(customSets[i].baseModelPrefab, false, newBoard.transform);
                    foreach (var element in baseModelRenderes)
                    {
                        element.enabled = false;
                    }

                    if (lastBaseModel != null)
                    {
                        Renderer[] renderers = lastBaseModel.GetComponentsInChildren<Renderer>();
                        foreach (var item in renderers)
                        {
                            item.enabled = false;
                        }
                    }

                    lastBaseModel = curRenderer;
                }

                if (customSets[i].modelPrefab != null)
                {
                    characterbase.CharacterModel.AddCustomHoverboardModel(customSets[i].modelPrefab, false, newBoard.transform);// hoverboardRoot.transform);
                }

                if (customSets[i].ability == BoardAbility.Jumper)
                {
                    boardState.IsJumper = true;

                    if (characterbase == character)
                    {
                        energyMagnet.JumperActivate();
                    }

                    playThruster _playThruster = newBoard.GetComponentInChildren<playThruster>();
                    if (_playThruster != null)
                    {
                        if (_playThruster.jumpStyle == playThruster.JumpStyle.Super)
                            _playThruster.Initialize();
                    }
                }
                else if (customSets[i].ability == BoardAbility.Lowrider)
                {
                    boardState.IsLowrider = true;
                    characterbase.SqueezeCollider.Add(this);
                }
                else if (customSets[i].ability == BoardAbility.Glider)
                {
                    boardState.IsGlider = true;

                    GliderAnimation gliderAnimation = newBoard.GetComponentInChildren<GliderAnimation>();
                    if (gliderAnimation != null)
                        gliderAnimation.AddEvents();
                }
                else if (customSets[i].ability == BoardAbility.Teleport)
                {
                    boardState.IsTeleporter = true;
                }
                else if (customSets[i].ability == BoardAbility.JumpHurdleEnergy)
                {
                    boardState.IsJumpHurdleEnergy = true;
                }
                else if (customSets[i].ability == BoardAbility.CrossHurdleEnergy)
                {
                    boardState.IsCrossHurdleEnergy = true;
                }
                else if (customSets[i].ability == BoardAbility.BigBattery)
                {
                    boardState.IsBigBattery = true;
                }
                else if (customSets[i].ability == BoardAbility.PressAdvantage)
                {
                    boardState.IsPressAdvantage = true;
                }
                else if (customSets[i].ability == BoardAbility.WeakMagnetic)
                {
                    boardState.IsWeakMagnetic = true;
                    boardState.CurrentAbility = customSets[i].AbilityBean;
                    Game.Instance.Modifiers.Add(WeakMagnetic.Instance);
                }
                else if (customSets[i].ability == BoardAbility.TimeAcceleration)
                {
                    boardState.IsTimeAcceleration = true;
                    AudioManager.Instance.PlaySFX("audio_ability_clean");
                }
                else if (customSets[i].ability == BoardAbility.Clean)
                {
                    boardState.IsClean = true;
                    RG.EventMgr.Instance.send(RG.EVENT_KEY.PVP_ABILITY_CLEAN, characterbase);
                }
                else if (customSets[i].ability == BoardAbility.UnstableShield)
                {
                    boardState.IsUnstableShield = true;
                    RG.EventMgr.Instance.send(RG.EVENT_KEY.PVP_ABILITY_UNSTABLE_SHIELD, characterbase);
                }
            }
        }

        return abilities.ToArray();
    }

    public void ResetAbility(CharacterBase characterbase)
    {
        BoardState boardState = characterbase.BoardState;
        boardState.CurrentAbility = null;
        if (boardState.IsJumper)
        {

            if (characterbase == character && !energyMagnet.ShouldBeActive)
                energyMagnet.Deactivate();

            boardState.IsJumper = false;
        }

        if (boardState.IsLowrider)
        {
            //在空中时 不修改碰撞体
            if (characterbase.IsGrounded.Value)
            {
                characterbase.SqueezeCollider.Remove(this);
                boardState.IsLowrider = false;
            }
            else
            {
                Debug.Log("hoverboardState.IsLowrider = true , characterbase.IsLowriderEnd = true");
                characterbase.IsLowriderEnd = true;
            }
        }

        if (boardState.IsGlider)
        {
            boardState.IsGlider = false;
        }
        if (boardState.IsTeleporter)
        {
            boardState.IsTeleporter = false;
        }
        if (boardState.IsCrossHurdleEnergy)
        {
            boardState.IsCrossHurdleEnergy = false;
        }
        if (boardState.IsJumpHurdleEnergy)
        {
            boardState.IsJumpHurdleEnergy = false;
        }
        if (boardState.IsBigBattery)
        {
            boardState.IsBigBattery = false;
        }
        if (boardState.IsPressAdvantage)
        {
            boardState.IsPressAdvantage = false;
        }
        if (boardState.IsWeakMagnetic)
        {
            boardState.IsWeakMagnetic = false;
        }
        if (boardState.IsTimeAcceleration)
        {
            boardState.IsTimeAcceleration = false;
        }
        if (boardState.IsClean)
        {
            boardState.IsClean = false;
        }
        if (boardState.IsUnstableShield)
        {
            boardState.IsUnstableShield = false;
        }

        characterbase.CharacterModel.DeleteBoardModels();
    }

    public string GetCurrentActiveHoverboard()
    {
        return RG.Board.Manager.Inst.Current?.itemsId;
    }

    public void HardReset()
    {
        character.BoardState.isActive = false;

        ResetAbility(character);

        character.CharacterModel.EndHoverboard();
    }

    public void CheckCurrentHoverboardLoaded()
    {
        if (Hoverboard.Instance.hoverboardSelector[0].hoverboardPrefab == null)
        {
            Hoverboard.Instance.hoverboardSelector[0].hoverboardPrefab = Resources.Load<GameObject>("Prefabs/Hoverboards/" + Hoverboard.Instance.hoverboardSelector[0].assetbundleName);
        }

        HoverboardSelection hoverboardCollection;
        if (hoverboardThatMatchBoardType.TryGetValue(GetCurrentActiveHoverboard(), out hoverboardCollection))
        {
            if (hoverboardCollection.hoverboardPrefab == null)
            {
                hoverboardCollection.hoverboardPrefab = Resources.Load<GameObject>("Prefabs/Hoverboards/" + hoverboardCollection.assetbundleName);
            }
        }
    }

    public void Initialize()
    {
        if (_isInitialized) return;
        intialize(); // 兼容原有初始化流程
        _isInitialized = true;
        if (OnInitialized != null) OnInitialized();
    }

}
