using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets.ResourceProviders;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.ResourceLocations;
using UnityEngine.ResourceManagement.ResourceProviders;
using System;
using UnityEngine.ResourceManagement.AsyncOperations;
using System.Threading.Tasks;
using RG.Common.Logger;

namespace RG
{
    public sealed class AddressableMgr
    {
        private static AddressableMgr _instance = new AddressableMgr();
        public static AddressableMgr Inst { get { return _instance; } }

        private Dictionary<string, AddressableLoader> _loaders = null;

        public string remoteCodeHash = string.Empty;

        private AddressableMgr()
        {
            _loaders = new Dictionary<string, AddressableLoader>();
        }

        public async void Load<T>(string assetId, System.Action<bool, T> action = null) where T : UnityEngine.Object
        {
            if (string.IsNullOrEmpty(assetId))
            {
                if (null != action)
                {
                    action.Invoke(false, null);
                }
                return;
            }
            AddressableLoader loader = null;
            if (!_loaders.TryGetValue(assetId, out loader))
            {
                loader = new AddressableLoader(assetId);
                _loaders.Add(assetId, loader);
            }
            await loader.Load(action);
        }

        public void Unload(string assetId)
        {
            if (string.IsNullOrEmpty(assetId))
            {
                return;
            }
            AddressableLoader loader = null;
            if (!_loaders.TryGetValue(assetId, out loader))
            {
                return;
            }
            if (null != loader)
            {
                loader.Unload();
            }
            _loaders.Remove(assetId);
        }

        public T GetAsset<T>(string assetId) where T : class
        {
            if (string.IsNullOrEmpty(assetId))
            {
                return null;
            }
            AddressableLoader loader = null;
            if (!_loaders.TryGetValue(assetId, out loader))
            {
                return null;
            }
            return loader.GetAsset<T>();
        }

        public string InitRemoteCodeHash()
        {
            foreach (var resourceLocator in Addressables.ResourceLocators)
            {
                if (resourceLocator.Locate(LuaEngine.CodeSetPath, typeof(object), out IList<IResourceLocation> locations))
                {
                    if (locations.Count > 0 && locations[0]?.Dependencies.Count > 0)
                    {
                        IResourceLocation remoteLocation = locations[0].Dependencies[(int)ContentCatalogProvider.DependencyHashIndex.Remote];
                        remoteCodeHash = ((AssetBundleRequestOptions)remoteLocation?.Data).Hash;
                    }
                }
            }
            return remoteCodeHash;
        }

        public async Task TryUpdateAddressable()
        {
#if HOTFIX_ENABLE	
            var handle = Addressables.CheckForCatalogUpdates(false);
            await handle.Task;

            if (handle.Status == AsyncOperationStatus.Succeeded)
            {
                List<string> catalogs = handle.Result;
                if (catalogs != null && catalogs.Count > 0)
                {
                    RGLogger.Log("Resource", "need update catalogs");
                    try
                    {
                        await Addressables.UpdateCatalogs(catalogs, true).Task;
                    }
                    catch (Exception e)
                    {
                        RGLogger.LogException("Resource", e);
                    }

                    LuaEngine.Instance.ResetLuaenv();
                    Common.Lua.Module.Manager.Inst.Init();
                    await LuaEngine.Instance.LoadHotfixCode();
                }
                else
                {
                    RGLogger.Log("Resource", "dont need update catalogs");
                }
            }
            Addressables.Release(handle);
#endif
        }
    }
}