using System.Collections;
using System.Collections.Generic;
using RG.Common;
using RG.Cameras;
using UnityEngine;
using RG.Characters.States;
using RG.Routes;
using ImpactX = ObstacleImpactUtil.ImpactX;
using ImpactY = ObstacleImpactUtil.ImpactY;
using ImpactZ = ObstacleImpactUtil.ImpactZ;
using RG.Level;
using RG;
using RG.Common;
using RG.Characters;

namespace RG.Characters
{
    public class Character : CharacterBase
    {
        private static Character _instance = null;
        public static Character Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType(typeof(Character)) as Character;
                }
                return _instance;
            }
        }

        #region Public Param
        public CollisionNotifier AttractCoinCollider;
        public CollisionNotifier AttractLongCoinCollider;
        public CollisionNotifier AttractEnergyCollider;
        public ParticleSystem HoverboardCrashParticleSystem;
        public float hitPoistionDistance;
        public bool CanAccelerateForward;
        public RunningState RunningState => this._characterState;
        public override int PlayerID => NetDirector.PlayerID;
        #endregion

        #region Private Param 
        private Vector3 _previousPos;
        private bool _isColliding = false;

        private RunningState _characterState;
        private CharacterCollider _characterCollider = null;
        private CharacterSpeed _runnerSpeed;
        private float _lastDistance;
        private int _checkTriggerCounter = 5;
        private Vector3 _centerControlVec3;
        private float _controlHeight;
        private Vector3 _centerHitVec3;
        private float _hitHeight;
        private string _hitTagCache;
        private Vector3 _fixedUpdatePosCache = Vector3.zero;
        private Vector3 _FixedUpdatePrePos = Vector3.zero;
        private float _lastCornerStumbleTime = 0;
        private Collider _lastCornerStumbleCollider = null;
        #endregion

        public delegate void OnCriticalHitDelegate(CriticalHitType type);
        public event OnCriticalHitDelegate OnCriticalHit;
        public delegate void OnPassedObstacleDelegate(ObstacleType type);
        public event OnPassedObstacleDelegate OnPassedObstacle;
        public bool IsVisibleShadow { get { return CharacterModel.IsVisibleShadow; } set { CharacterModel.IsVisibleShadow = value; } }

        #region Properties
        public bool IsStumbling
        {
            get
            {
                return _isColliding;
            }
            set
            {
                _isColliding = value;
            }
        }

        public Vector3 PreviousFixedUpdateTransformPosition
        {
            get
            {
                return _previousPos;
            }
            set
            {
                if (_previousPos != value)
                {
                    _previousPos = value;
                }
            }
        }

        #endregion

        #region HCharSpeed
        public override CharacterSpeedBase charSpeed
        {
            get
            {
                if (_runnerSpeed == null)
                {
                    _runnerSpeed = GetComponent<CharacterSpeed>();
                }
                return _runnerSpeed;
            }
        }

        public override float CurrentSpeed
        {
            get
            {
                return charSpeed.NormalizedSpeed;
            }
        }

        #endregion

        #region Initialization
        private bool _isInitialized = false;
        public event System.Action OnInitialized;
        public bool IsInitialized => _isInitialized;
        #endregion

        public override void Initialize()
        {
            if (_isInitialized) return;
            
            base.Initialize();
            this.InitModel();
            this.InitValue();
            this.RegisterGameEvent();
            
            _isInitialized = true;
            OnInitialized?.Invoke();
        }

        public void SetCharacterModel(CharacterModel model)
        {
            CharacterModel = model;
            InitModel();
        }

        private void InitModel()
        {
            if (CharacterModel == null)
            {
                Debug.LogWarning("[Character] InitModel: CharacterModel 尚未由 CharacterRendering 初始化。");
                return;
            }
            CharacterRoot = CharacterModel.transform;
        }

        private void OnInGameValueChanged(bool value)
        {
            if (!value)
            {
                charSpeed.End();
                StopAllCoroutines();
                CharacterController.enabled = true;
                StopColliding = false;
            }
        }

        /// <summary>
        /// 蹲下时需要挤压碰撞盒
        /// </summary>
        /// <param name="value"></param>
        private void OnSqueezeColliderChanged(bool value)
        {
            if (value)
            {
                CharacterController.height = 4f;
                CharacterController.center = new Vector3(0, 2f, _centerControlVec3.z);
                CharacterTrigger.height = 4f;
                CharacterTrigger.center = new Vector3(0, 4f, _centerHitVec3.z);
            }
            else
            {
                CharacterController.center = _centerControlVec3;
                CharacterController.height = _controlHeight;
                CharacterTrigger.center = _centerHitVec3;
                CharacterTrigger.height = _hitHeight;
            }
        }

        private void RegisterGameEvent()
        {
            RouteManager.Instance.OnRouteCreated += OnCreatedRoute;
            _game.IsInGame.OnChange += this.OnInGameValueChanged;
            SqueezeCollider.OnChange += this.OnSqueezeColliderChanged;
            CharacterTriggerNotifier.TriggerEnter += OnCharacterTriggerEnter;
            CharacterTriggerNotifier.TriggerExit += OnCharacterTriggerExit;
        }

        private void InitValue()
        {
            _game = Game.Instance;
            _characterState = GetState<RunningState>(); // TODO: 状态系统重构兼容性写法，后续彻底移除单例
            _characterCollider = CharacterCollider.Instance;
            CharacterCamera = CharacterCamera.Instance;

            _centerControlVec3 = CharacterController.center;
            _controlHeight = CharacterController.height;
            _centerHitVec3 = CharacterTrigger.center;
            _hitHeight = CharacterTrigger.height;
        }

        private void ClearFrame()
        {
            CharacterTriggerNotifier.ClearFrame();
            AttractCoinCollider.ClearFrame();
            AttractLongCoinCollider.ClearFrame();
        }

        public override void Awake()
        {
            base.Awake();
            _instance = this;
            GamePositionRoot.Instance.AddOffsetTransform(transform);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (RouteManager.IsInstantiated)
            {
                RouteManager.Instance.OnRouteCreated -= OnCreatedRoute;
            }
        }

        protected override void LogicLateUpdate(int fpsCount)
        {
            if (_movedSinceLastFixedUpdate)
            {
                _FixedUpdatePrePos = _fixedUpdatePosCache;
                _fixedUpdatePosCache = transform.position;
                _movedSinceLastFixedUpdate = false;
            }

            PreviousFixedUpdateTransformPosition = transform.position;

            this.ClearFrame();
        }

        protected override void LogicUpdate(int fpsCount)
        {
            Vector3 position = TransformPosition;

            if (position.y < 0)
            {
                position.y = 1f;
                transform.position = position;
                CachePositions();
            }

            if (_game == null || UnityEngine.Time.timeScale == 0)
                return;

            this.CheckHitTrigger();
            var a = GetModelRotation();
            var b = Quaternion.Euler(0f, a.eulerAngles.y, a.eulerAngles.z);
            SetModelRotation(Quaternion.RotateTowards(a, b, 90f * TimeMgr.deltaTime));


            if (_lastCornerStumbleCollider != null && _lastCornerStumbleTime + CharacterConstant.CORNER_MIN_HIT_TIME < TimeMgr.time)
            {
                _lastCornerStumbleCollider = null;
            }
            _lastDistance = GamePosition.z;
        }

        private void CheckHitTrigger()
        {
            if (GamePosition.z == _lastDistance && _game.IsInGame.Value && !isStopRunning)
            {
                if (_checkTriggerCounter > 0)
                {
                    if (!CharacterTrigger.enabled)
                    {
                        //重启一次碰撞逻辑
                        CharacterTrigger.enabled = true;
                    }
                    _checkTriggerCounter -= 1;
                }
                else if (!Game.Instance.IsOnOpeningShot && GamePosition.z > 10)
                {
                    if (_lastCornerStumbleCollider != null)
                    {
                        OnCharacterCollider(_lastCornerStumbleCollider);
                        _lastCornerStumbleCollider = null;
                    }
                    else
                    {
                        //预备重启一次碰撞逻辑
                        CharacterTrigger.enabled = false;
                        _checkTriggerCounter = 10;
                    }

                    if (IsStumbling)
                    {
                        StopStumble();
                    }
                }
            }
            else if (_checkTriggerCounter != 10)
                _checkTriggerCounter = 10;
        }

        public void Reset()
        {
            IsVisibleShadow = true;
            ResetModelRotation();
            CurrentStumbleState = StumbleState.Standard;
            _initialTrackIndex = 1;
            CurrentRoadIndex = _initialTrackIndex;
            _trackIndexTarget = _initialTrackIndex;

            x = Route.GetTrackX(CurrentRoadIndex);
            _trackIndexPosition = CurrentRoadIndex;

            _trackMovement = 0;
            _trackMovementNext = 0;

            SqueezeCollider.Clear();

            SetGamePosition(Route.GetPosition(x, 0) + Vector3.up * 5f);
            CharacterController.Move(-5 * Vector3.up);

            CachePositions();

            verticalSpeed = 0f;
            _superSneakersJump = null;

            JumpHeight = jumpHeightNormal;
            inAirJump = false;
            IsJumping = false;
            IsRolling = false; ;
            _startedJumpFromGround = false;
            _jumpUpperCollision = false;
            _jumpUpperCollisionEvaded = true;

            LastGroundedY = 0f;
            IsColliderCar = false;
            this._characterCollider.ClearCache();
        }

        public void ReadyToRun()
        {
            StartStumble();
            sameLaneTimeStamp = TimeMgr.time;
        }

        public void Revive()
        {
            CharacterRendering.ResetTeleportFx();
            this.IsDeath = false;
            NotifyOnRevive();
            if (_isColliding) StopStumble();

            IsJumping = true;
            IsFalling = false;

            CanAccelerateForward = true;
            verticalSpeed = CalculateJumpVerticalSpeed(15f);

            _superSneakersJump = null;

            if (_jumpUpperCollision)
            {
                float yMove = CharacterController.bounds.min.y - _jumpUpperCollisionColliderMinY;
                if (yMove < 0)
                    CharacterController.Move(yMove * Vector3.up);
            }

            CachePositions();
            _jumpUpperCollision = false;
            _jumpUpperCollisionEvaded = true;
        }

        public override void ChangeTrackBy(int trackDelta, float duration)
        {
            if (trackDelta < 0)
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.DodgeLeft, _trackIndexTarget.ToString(), _trackIndexPosition.ToString());
            else
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.DodgeRight, _trackIndexTarget.ToString(), _trackIndexPosition.ToString());
            base.ChangeTrackBy(trackDelta, duration);
        }

        public void ForceLeaveSubway()
        {
            IsColliderCar = false;
            this._characterCollider.ClearCache();
        }

        public override void Roll()
        {
            if (IsRolling) return;
            base.Roll();

            if (!_characterState.transitionFromHeight)
                verticalSpeed = -CalculateJumpVerticalSpeed(JumpHeight);
            else
                verticalSpeed = CharacterConstant.ROLL_VERTICAL_SPEED;
        }

        public override bool Jump()
        {
            bool canJump = base.Jump();
            if (canJump)
            {
                PvpMgr.Inst.SendLogicalOperation(LogicalOperationType.Jump);
            }
            return canJump;
        }

        public void MoveWithGravity()
        {
            if (CharacterController.enabled)
            {
                verticalSpeed -= Gravity * TimeMgr.deltaTime;
                if (verticalSpeed > 0)
                {
                    verticalSpeed = 0;
                }

                Vector3 verticalMove = verticalSpeed * TimeMgr.deltaTime * Vector3.up;
                CharacterController.Move(verticalMove);

                CachePositions();
            }
        }

        public void StopStumble()
        {
            _isColliding = false;
        }

        private void _DoStumble(StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, Collider collider, bool isCriticalHit)
        {
            if (collider != null && !isCriticalHit)
            {
                BumpProperty bump = collider.GetComponent<BumpProperty>();
                if (bump != null)
                {
                    bump.CharacterStumble();
                }
            }
            else if (stumbleType == StumbleType.Side)
            {
                charSpeed.ChangeState(CharacterSpeedState.Bump, speedLossRatio: charSpeed.Property.stumbleSpeedLossPercentage);
            }
        }

        public override IEnumerator ChangeTrackCoroutine(int move, float duration)
        {
            _trackMovement = move;
            _trackMovementNext = 0;

            int newTrackIndex = _trackIndexTarget + move;

            float trackChangeIndexDistance = Mathf.Abs(newTrackIndex - _trackIndexPosition);
            float trackIndexPositionBegin = _trackIndexPosition;
            float startX = x;
            float endX = Route.GetTrackX(newTrackIndex);

            float dir = Mathf.Sign(move);

            float startRotation = _characterRotation;

            var trackChangeDir = move < 0 ? OnChangeTrackDirection.Left : OnChangeTrackDirection.Right;

            if (newTrackIndex < RouteConstants.TRACK_INDEX_LEFT)
            {
                if (CurrentStumbleState == StumbleState.Standard)
                {
                    HandleStumble(StumbleType.Side, StumbleHorizontalHit.Left, StumbleVerticalHit.Middle, null);
                }
                else if (CurrentStumbleState == StumbleState.Safe)
                {
                    RequestSafeStumble(StumbleType.Side, move > 0 ? StumbleHorizontalHit.Left : StumbleHorizontalHit.Right, StumbleVerticalHit.Middle, null);
                }
                yield break;
            }

            if (newTrackIndex >= RouteConstants.NUMBER_OF_TRACKS)
            {
                if (CurrentStumbleState == StumbleState.Standard)
                {
                    HandleStumble(StumbleType.Side, StumbleHorizontalHit.Right, StumbleVerticalHit.Middle, null);
                }
                else if (CurrentStumbleState == StumbleState.Safe)
                {
                    RequestSafeStumble(StumbleType.Side, move > 0 ? StumbleHorizontalHit.Left : StumbleHorizontalHit.Right, StumbleVerticalHit.Middle, null);
                }
                yield break;
            }
            SendChangeTrackEvent(trackChangeDir);

            _trackIndexTarget = newTrackIndex;

            yield return StartCoroutine(Utility.SetInterval(trackChangeIndexDistance * duration, t =>
            {
                _trackIndexPosition = Mathf.Lerp(trackIndexPositionBegin, newTrackIndex, t);
                x = Mathf.Lerp(startX, endX, t);

                _characterRotation = Utility.Bell(t) * dir * CharacterConstant.ANGLE + Mathf.Lerp(startRotation, 0f, t);
                CharacterRoot.localRotation = Quaternion.Euler(0f, _characterRotation, 0f);
            }));

            CurrentRoadIndex = newTrackIndex;
            _trackMovement = 0;

            if (_trackMovementNext != 0)
            {
                StartCoroutine(ChangeTrackCoroutine(_trackMovementNext, duration));
            }
        }

        private void OnCharacterTriggerEnter(Collider collider)
        {
            hitPoistionDistance = TransformPosition.z;

            //检查是否满足碰撞条件!
            if (!this._characterCollider.handleColliderTag(collider))
            {
                return;
            }
            if (!this._characterCollider.HandlePVPCollider(collider))
            {
                return;
            }
            if (!this._characterCollider.HandleDefaultCollider(collider))
            {
                return;
            }
            if (!this._characterCollider.HandleHitCollider(collider))
            {
                return;
            }

            if (_lastCornerStumbleCollider != null && collider != _lastCornerStumbleCollider)
            {
                OnCharacterCollider(_lastCornerStumbleCollider);
                _lastCornerStumbleCollider = null;
            }

            _hitTagCache = collider.tag;

            ImpactX obstacleImpactX = ObstacleImpactUtil.GetObstacleImpactX(collider, CharacterTrigger, CharacterConstant.COLLIDER_TRACK_WIDTH);
            ImpactY characterImpactY = ObstacleImpactUtil.GetCharacterImpactY(collider, CharacterTrigger);
            ImpactZ obstacleImpactZ = ObstacleImpactUtil.GetObstacleImpactZ(collider, TransformPosition, CharacterConstant.CORNER_STUMBLE);

            float colliderCenter_x = (collider.bounds.min.x + collider.bounds.max.x) / 2;
            float character_x = TransformPosition.x;

            int hitDir;
            if (character_x < colliderCenter_x)
                hitDir = 1;
            else if (character_x > colliderCenter_x)
                hitDir = -1;
            else
                hitDir = 0;

            bool characterIsMovingTowardsCollider = (hitDir == 0) || (_trackMovement == hitDir);
            bool isBeforeCollider = CharacterTrigger.bounds.center.z < collider.bounds.min.z;
            bool impactZbeforeIsMiddle = obstacleImpactZ == ImpactZ.Before && !isBeforeCollider && characterIsMovingTowardsCollider;
            bool jumpUpperCollisionRepeated = _jumpUpperCollision;

            if (IsJumping && characterImpactY == ImpactY.Upper && !CharacterController.isGrounded)
            {
                _jumpUpperCollision = true;
                _jumpUpperCollisionEvaded = false;
                verticalSpeed = 0f;
                if (!jumpUpperCollisionRepeated)
                {
                    _jumpUpperCollisionColliderMinY = collider.bounds.min.y;
                }
            }

            if (obstacleImpactZ == ImpactZ.Middle || impactZbeforeIsMiddle)
            {
                if (_trackMovement != 0)
                {
                    float duration = 0.5f;
                    ChangeTrackBy(-_trackMovement, duration);
                }

                if (obstacleImpactX == ImpactX.Left)
                {
                    HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Left, StumbleVerticalHit.Middle, collider);
                }
                else if (obstacleImpactX == ImpactX.Right)
                {
                    HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Right, StumbleVerticalHit.Middle, collider);
                }
            }
            else
            {
                if (obstacleImpactX == ImpactX.Middle || _trackMovement == 0)
                {
                    if (obstacleImpactZ == ImpactZ.Before)
                    {
                        if (characterImpactY == ImpactY.Lower)
                        {
                            if (!IsGliderboard)
                                verticalSpeed = CalculateJumpVerticalSpeed(8f);
                            else
                                verticalSpeed = CalculateJumpVerticalSpeed(1.5f);

                            if (!IsTeleboard)
                            {
                                HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Center, StumbleVerticalHit.Lower, collider);
                            }
                            else
                            {
                                SendResetTeleportFXEvent();
                            }
                        }
                        else if (collider.gameObject.CompareTag("HitMovingTrain"))
                        {
                            this.CanAccelerateForward = false;
                            HitByTrainSequence();
                            OnCharacterCollider(collider);
                        }
                        else if (characterImpactY == ImpactY.Middle)
                        {
                            this.CanAccelerateForward = false;
                            if (!IsTeleboard)
                            {
                                HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Center, StumbleVerticalHit.Middle, collider, isCriticalHit: true);
                            }
                            else
                            {
                                SendResetTeleportFXEvent();
                            }
                            OnCharacterCollider(collider);
                        }
                        else
                        {
                            if (!IsTeleboard)
                            {
                                HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Center, StumbleVerticalHit.Upper, collider, isCriticalHit: true);
                            }
                            else
                            {
                                SendResetTeleportFXEvent();
                            }
                            OnCharacterCollider(collider);
                        }
                    }
                }
                else
                {
                    if (obstacleImpactZ == ImpactZ.Before && characterIsMovingTowardsCollider)
                    {
                        if (collider.gameObject.CompareTag("HitMovingTrain"))
                        {
                            this.CanAccelerateForward = false;
                            HitByTrainSequence();
                            OnCharacterCollider(collider);
                        }
                        else if (collider.gameObject.layer == Layers.HitBounceOnly.Value) // Ramps
                        {
                            if (!IsTeleboard)
                                HandleStumble(StumbleType.Normal, StumbleHorizontalHit.Center, StumbleVerticalHit.Lower, collider);
                        }
                        else
                        {
                            ForceChangeTrack(-_trackMovement, 0.5f);
                        }
                    }
                    else if (collider.gameObject.layer == Layers.HitBounceOnly.Value)
                    {
                        ForceChangeTrack(-_trackMovement, 0.5f);
                    }

                    if (obstacleImpactX == ImpactX.Left)
                    {
                        if (!IsTeleboard)
                            HandleStumble(StumbleType.Normal, StumbleHorizontalHit.LeftCorner, StumbleVerticalHit.Middle, collider);
                    }
                    else if (obstacleImpactX == ImpactX.Right)
                    {
                        if (!IsTeleboard)
                            HandleStumble(StumbleType.Normal, StumbleHorizontalHit.RightCorner, StumbleVerticalHit.Middle, collider);
                    }
                }
            }
        }

        private void OnCharacterTriggerExit(Collider collider)
        {
            var type = this._characterCollider.ObstacleTagToType(collider.tag);
            if (!this._characterCollider.Stumbling && this.CharacterModel.isOwnerModel)
            {
                if (type == ObstacleType.JumpBarrier || type == ObstacleType.JumpHighBarrier)
                {
                    EventMgr.Instance.send(EVENT_KEY.PVP_ABILITY_JUMP_HURDLE_ENERGY, Character.Instance);
                }
                else if (type == ObstacleType.RollBarrier)
                {
                    EventMgr.Instance.send(EVENT_KEY.PVP_ABILITY_CROSS_HURDLE_ENERGY, Character.Instance);
                }
            }

            this._characterCollider.ResetPVPCollider();

            if (!this._characterCollider.HandleExitSubwayCollider(collider))
            {
                return;
            }

            if (collider.gameObject.layer == Layers.RealPVP.Value)
            {
                Powerup powerup = collider.GetComponent<Powerup>();
                if (powerup != null)
                {
                    powerup.TriggerOut();
                    return;
                }

                PFXBase fx = collider.GetComponent<PFXBase>();
                if (fx != null)
                {
                    fx.TriggerOut();
                    return;
                }
            }

            if (type == this._characterCollider.FinallyHitObstacle && this._characterCollider.FinallyHitObstacleTrack == CurrentRoadIndex)
            {
                if (OnPassedObstacle != null)
                    OnPassedObstacle(type);
            }
        }

        private void HitByTrainSequence()
        {
            if (BoardState.isActive)
            {
                NotifyOnJumpIfHitByTrain();
                return;
            }

            SendHitTrainEvent();
        }

        public override bool IsRunningOnGround()
        {
            return _characterState.currentRunPosition == RunningState.RunPositions.ground;
        }

        public override bool IsRunningAir()
        {
            return _characterState.currentRunPosition == RunningState.RunPositions.air;
        }

        private void StartStumble(bool safeStumble = false)
        {
            _isColliding = !safeStumble;
        }

        public void RequestSafeStumble(StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, Collider collider)
        {
            //TODO: Should we always allow a RequestStumble() to call HandleStumble()? Current, yes.
            //      Maybe we want to check if its already stumbling, etc.. first, here.
            HandleStumble(stumbleType, horizontalHit, verticalHit, collider, true);
            _isColliding = false;
        }

        public void OpenHandleStumble
            (
                StumbleType stumbleType,
                StumbleHorizontalHit horizontalHit,
                StumbleVerticalHit verticalHit,
                Collider collider,
                bool safeStumble = false,
                bool isCriticalHit = false
            )
        {
            this.HandleStumble(stumbleType, horizontalHit, verticalHit, collider, safeStumble, isCriticalHit);
        }

        private void HandleStumble
            (
                StumbleType stumbleType,
                StumbleHorizontalHit horizontalHit,
                StumbleVerticalHit verticalHit,
                Collider collider,
                bool safeStumble = false,
                bool isCriticalHit = false
            )
        {
            this._characterCollider.Stumbling = true;
            if (collider != null)
            {
                this._characterCollider.SetPVPCollider(collider.GetComponent<BoardCollider>());
            }
            this.CustomFLow(stumbleType);
            StumbleAniType aniType = StumbleAniType.Stumble;
            if (stumbleType == StumbleType.Bush)
            {
                aniType = StumbleAniType.StumbleMix;
                _DoStumble(stumbleType, horizontalHit, verticalHit, collider, isCriticalHit);
                NotifyOnStumble(aniType.ToString(), stumbleType, horizontalHit, verticalHit, collider != null ? collider.name : "side", safeStumble);
                StartStumble(safeStumble);
                return;
            }

            _lastCornerStumbleTime = TimeMgr.time;
            _lastCornerStumbleCollider = collider;
            if (stumbleType == StumbleType.Side)
            {

                if (horizontalHit == StumbleHorizontalHit.LeftCorner ||
                    horizontalHit == StumbleHorizontalHit.Left)
                {
                    aniType = StumbleAniType.StumbleLeftSide;

                }

                if (horizontalHit == StumbleHorizontalHit.RightCorner ||
                    horizontalHit == StumbleHorizontalHit.Right)
                {
                    aniType = StumbleAniType.StumbleRightSide;
                }

            }
            else
            {
                if (horizontalHit == StumbleHorizontalHit.Center)
                {
                    if (verticalHit == StumbleVerticalHit.Lower)
                    {
                        if (BoardState.isActive)
                        {
                            aniType = StumbleAniType.StumbleMix;
                        }
                        else
                        {
                            aniType = StumbleAniType.Stumble;
                        }
                    }
                    else if (verticalHit == StumbleVerticalHit.Middle)
                    {
                        aniType = StumbleAniType.HitMid;
                    }
                    else if (verticalHit == StumbleVerticalHit.Upper)
                    {
                        aniType = StumbleAniType.Stumble;
                    }
                }
                else
                {
                    if (horizontalHit == StumbleHorizontalHit.Left)
                    {
                        aniType = BoardState.isActive ? StumbleAniType.StumbleLeftSide : StumbleAniType.StumbleLeftSide;
                    }
                    else if (horizontalHit == StumbleHorizontalHit.LeftCorner)
                    {
                        aniType = StumbleAniType.StumbleLeftCorner;
                    }
                    else if (horizontalHit == StumbleHorizontalHit.Right)
                    {
                        aniType = BoardState.isActive ? StumbleAniType.StumbleRightCorner : StumbleAniType.StumbleRightSide;
                    }
                    else if (horizontalHit == StumbleHorizontalHit.RightCorner)
                    {
                        aniType = StumbleAniType.StumbleRightCorner;
                    }

                }
            }
            _DoStumble(stumbleType, horizontalHit, verticalHit, collider, isCriticalHit);
            NotifyOnStumble(aniType.ToString(), stumbleType, horizontalHit, verticalHit, collider != null ? collider.name : "side", safeStumble);
            StartStumble(safeStumble);
        }

        private void CustomFLow(StumbleType type)
        {
            Dictionary<string, object> ext_map = new Dictionary<string, object>();
            ext_map.Add("roomId", PvpMgr.Inst.RoomID);
            ext_map.Add("gamePlay", RG.GamePlayMgr.Inst.CurrentGamePlay.Id);
            RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.PVPGameInfo, "Stumble", type.ToString(), RG.Common.Json.Serialize(ext_map));
        }

        private void OnCharacterCollider(Collider collider)
        {
            if (collider != null)
            {
                BumpProperty bump = collider.GetComponent<BumpProperty>();
                if (bump != null)
                {
                    bump.CharacterBump();
                }
                else if (Utility.IsChild(collider.transform, "GeneratedEnvironment"))
                {
                    if (IsStumbling)
                    {
                        StopStumble();
                    }
                    CriticalHitGeneratedEnvironment();
                }
            }

            if (OnCriticalHit != null)
            {
                OnCriticalHit(this._characterCollider.GetHitTypeFromTag(_hitTagCache));
            }
        }



        private void OnCreatedRoute(object sender, RouteManager.RouteCreatedEventArgs args)
        {
            AlternateRoute alternateRoute = args.Route as AlternateRoute;
            if (alternateRoute != null)
            {
                alternateRoute.OnCharacterEntrySkipped += OnCharacterEntrySkipped;
                alternateRoute.OnCharacterEntered += OnCharacterEntered;
                alternateRoute.OnDestructing += OnDestructing;
            }
        }

        private void OnDestructing(object sender, System.EventArgs args)
        {
            AlternateRoute alternateRoute = sender as AlternateRoute;
            if (alternateRoute != null)
            {
                alternateRoute.OnDestructing -= OnDestructing;
                alternateRoute.OnCharacterEntered -= OnCharacterEntered;
                alternateRoute.OnCharacterEntrySkipped -= OnCharacterEntrySkipped;
            }
        }

        private void OnCharacterEntered(object sender, System.EventArgs args)
        {
            CharacterModel.IsVisibleShadow = false;
            CurrentStumbleState = StumbleState.Safe;
        }

        private void OnCharacterEntrySkipped(object sender, Route.CharacterEntrySkippedEventArgs args)
        {
            if (args.HasCharacterStartedEntryTransition)
            {
                StartCoroutine(ResetAfterRouteDelayed(false));
            }
        }

        private IEnumerator ResetAfterRouteDelayed(bool isRouteDestructing)
        {
            yield return null;

            if (OnRouteDone != null)
            {
                OnRouteDone(isRouteDestructing);
            }


            if (!isRouteDestructing)
            {
                _game.Modifiers.Resume();
            }
            else
            {
                _game.Modifiers.Reset();
            }
        }

        public CollisionNotifier CoinMagnetCollider;
        public CollisionNotifier CoinMagnetLongCollider;
    }
}