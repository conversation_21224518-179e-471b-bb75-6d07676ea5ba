using UnityEngine;
using RG.Characters;

namespace RG.Characters.Components
{
    /// <summary>
    /// 角色组件基类，所有功能组件需继承
    /// Base class for all character components
    /// </summary>
    public abstract class CharacterComponent : MonoBehaviour
    {
        /// <summary>
        /// 组件所属角色对象
        /// The character this component is attached to
        /// </summary>
        protected CharacterBase _character;

        /// <summary>
        /// 初始化组件，注入宿主角色
        /// Initialize component with character
        /// </summary>
        public virtual void Initialize(CharacterBase character)
        {
            _character = character;
        }
    }
} 