﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using RG.Characters;
using RG.Common;
using RG.Board;
using RG.Abilities;
using SubwayPvp.Core.Audio;

public class BoardState : PvpLogicBehaviour
{
    /// <summary>
    /// 是否附加不稳定护盾技能.
    /// </summary>
    [HideInInspector]
    public bool IsUnstableShield = false;
    /// <summary>
    /// 是否附加清除技能.
    /// </summary>
    [HideInInspector]
    public bool IsClean = false;
    /// <summary>
    /// 是否附加减少负面效果时间技能
    /// </summary>
    [HideInInspector]
    public bool IsTimeAcceleration = false;
    /// <summary>
    /// 是否附加能量瓶吸取技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsWeakMagnetic = false;
    /// <summary>
    /// 是否附加超越加成技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsPressAdvantage = false;
    /// <summary>
    /// 是否附加超级能量瓶技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsBigBattery = false;
    /// <summary>
    /// 是否附加下滚障碍加能量技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsCrossHurdleEnergy = false;
    /// <summary>
    /// 是否附加跳跃障碍加能量技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsJumpHurdleEnergy = false;
    /// <summary>
    /// 是否附加超级跳跃技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsJumper = false;
    /// <summary>
    /// 是否附加滑翔技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsGlider = false;
    /// <summary>
    /// 是否附加瞬间移动技能
    /// </summary>
    /// <value></value>
    [HideInInspector]
    public bool IsTeleporter = false; //Teleport
    /// <summary>
    /// 是否附加躺倒技能
    /// </summary>
    bool isLowrider = false;
    public bool IsLowrider { get { return isLowrider; } set { this.OnLowriderStateChanged(value); } }

    [HideInInspector]
    public bool isActive = false;

    // public delegate void OnSwitchToHoverboardDelegate(GameObject hoverbooard, BoardAbility[] abilities, RG.Board.Board board = null);
    // public event OnSwitchToHoverboardDelegate OnSwitchToHoverboard;
    public delegate void OnEndHoverboardDelegate();
    public event OnEndHoverboardDelegate OnEndHoverboard;
    public delegate void OnHoverboardJumpDelegate();
    public event OnHoverboardJumpDelegate OnJump;
    public delegate void OnRunDelegate();
    public event OnRunDelegate OnRun;

    public List<HoverboardSelection.CustomSet> CustomSets = null;
    public Ability CurrentAbility = null;

    private CharacterBase _characterBase;
    private GameObject _goBoard;

    public void Initialize(CharacterBase cBase)
    {
        _characterBase = cBase;
    }

    public void CreateHoverboard(Board board, bool isCharacter = false)
    {
        _characterBase.CharacterModel.AttachPoint1.gameObject.SetActive(true);
        GameObject boardGo = board.GetModelPrefab(null);
        if (boardGo == null)
            return;
        isActive = true;
        _goBoard = _characterBase.CharacterModel.AddHoverboardModel(boardGo, board);
        Hoverboard.Instance.ShowBoardEff(_goBoard, _characterBase, board);

        GliderAnimation gliderAnimation = _goBoard.GetComponentInChildren<GliderAnimation>();
        if (gliderAnimation != null)
        {
            gliderAnimation.CurCharacter = _characterBase;
        }

        BoardAbility[] abilities = Hoverboard.Instance.SetupAbility(_goBoard, _characterBase, board);
        _characterBase.CharacterRendering.OnSwitchToHoverboard(_goBoard, abilities, board);
        if (isCharacter)
        {
            AudioManager.Instance.PlaySFX(Hoverboard.Instance.StartSound.Clip);
        }
        else
        {
            Utility.SetLayerRecursively(_goBoard.transform, Layers.Character.Value);
        }
        _characterBase.CharacterModel.UseHoverboard();
    }


    public void DisHoverboard()
    {
        isActive = false;
        Hoverboard.Instance.ResetAbility(_characterBase);
        AudioManager.Instance.PlaySFX(Hoverboard.Instance.powerDownSound.Clip);
        TriggerOnEndHoverboard();
        this.CustomSets = null;
        if (_characterBase.IsFalling || _characterBase.IsJumping)
        {
            TriggerOnJump();
        }
        else
        {
            TriggerOnRun();
        }
        Destroy(_goBoard);

        _characterBase.CharacterModel.EndHoverboard();
    }
    public void TriggerOnJump()
    {
        if (OnJump != null)
            OnJump();
    }
    public void TriggerOnRun()
    {
        if (OnRun != null)
            OnRun();
    }

    public void TriggerOnEndHoverboard()
    {
        if (OnEndHoverboard != null)
        {
            OnEndHoverboard();
        }
    }

    protected override void LogicUpdate(int fpsCount)
    {

    }

    private void OnLowriderStateChanged(bool b)
    {
        this.isLowrider = b;
        this._characterBase.CharacterRendering.OnLowriderStateChanged(b);
    }
}
