using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class JumpingState : CharacterState
    {
        private float _jumpHeight;
        private float _jumpStartTime;
        private float _jumpDuration;
        private bool _isSuperJump;

        public override void Enter()
        {
            _jumpStartTime = Time.time;
            _jumpHeight = Character.jumpHeightNormal;
            _isSuperJump = false;

            if (Character.IsJumpingHigher)
            {
                _jumpHeight = Character.SuperJumpAltitude;
                _isSuperJump = true;
            }

            Character.verticalSpeed = Character.CalculateJumpVerticalSpeed(_jumpHeight);
            Character.IsJumping = true;
            Character.OnJump?.Invoke(_jumpHeight, true);
        }

        public override void Exit()
        {
            Character.IsJumping = false;
        }

        public override void OnStateUpdate()
        {
            // 应用重力
            Character.ApplyGravity();

            // 检查是否落地
            if (Character.IsGrounded.Value)
            {
                Character.NotifyOnLanding();
                Character.CharacterStateManager.ChangeState<RunningState>();
            }

            // 检查是否可以二段跳
            if (Character.inAirJump && !Character._willSecondJump)
            {
                Character._willSecondJump = true;
                Character.verticalSpeed = Character.CalculateJumpVerticalSpeed(_jumpHeight);
                Character.OnJump?.Invoke(_jumpHeight, true);
            }
        }

        public override bool CanTransitionTo(CharacterState nextState)
        {
            if (nextState == null)
                return false;

            // 检查是否可以切换到目标状态
            if (nextState is RunningState)
            {
                return Character.IsGrounded.Value;
            }
            else if (nextState is DeathState)
            {
                return true;
            }

            return false;
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            if (Character.Instance.isStopRunning) return;

            float currentSpeed = Character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }

            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(-1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(-1, RunningState.CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Right:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(+1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(+1, RunningState.CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Down:
                    Character.Roll();
                    break;
            }
        }
    }
} 