using UnityEngine;
using System.Collections;
using System.Linq;
using RG.Cameras;
using RG.Common.Utilities;
using RG.Inputs;
using RG.Routes;
using System.Collections.Generic;
using RG.Level;

namespace RG.Characters.States
{
    public class SteamJumpState : CharacterState
    {
        public bool isActive = false;
        public bool isFromRunnerState = false;
        public float cameraAimOffset = 20f;
        public float jumpHeight = 95f;
        public float jumpDistance = 800f;
        public float characterChangeTrackLength = 60;
        [SerializeField] public AnimationCurve jumpCurve;
        [SerializeField] public AnimationCurve cameraCurve;
        [SerializeField] public AnimationCurve cameraAimFollowCurve;
        public float smoothCameraXDuration = 0.05f;
        public float topPosition = 0.5f;
        public float fadeInPosition = 0.1f;
        public float hangTimePosition = 0.5f;
        public float finalJumpSpeed = 0f;

        public delegate void OnStartDelegate();
        public OnStartDelegate OnStart;
        public delegate void OnReachedTopDelegate();
        public OnStartDelegate OnHangTime;
        public delegate void OnStopDelegate();
        public OnStopDelegate OnStop;

        private Transform _characterCameraTransform;

        private bool _reachedHangtime = false;

        public static SteamJumpState Instance { get { return instance ?? (instance = UnityEngine.Object.FindObjectOfType<SteamJumpState>()); } }
        private static SteamJumpState instance = null;

        // 状态初始化（进入跑酷时由外部调用）
        public override void InitState()
        {
            // TODO: 事件注册、依赖注入等
        }

        // 状态清理（退出一局时由外部调用）
        public override void ClearState()
        {
            // TODO: 事件注销、引用清理等
        }

        public override IEnumerator Begin()
        {
            isActive = true;
            this.isFromRunnerState = true;

            GamePositionRoot.Instance.AddLock(this);

            var character = _character;
            character.inAirJump = false;
            character.IsGrounded.Value = false;

            NotifyOnStart();
            _game.Modifiers.PauseInJetpackMode();

            if (character.IsStumbling)
                character.StopStumble();

            _characterController.detectCollisions = false;

            Vector3 startGamePosition = character.GamePosition;
            Vector3 endGamePosition = startGamePosition + Vector3.forward * jumpDistance;

            float speed = character.CurrentSpeed;
            Vector3 cameraPositionStart = _characterCamera.Position;
            Vector3 CameraTargetStartOffset = _characterCamera.Position - _characterCamera.Target;
            Vector3 cameraOffset = new Vector3(0, 33, 0);
            Vector3 startCameraOffset = _characterCameraTransform.position - character.TransformPosition;
            startCameraOffset = new Vector3(startCameraOffset.x, startCameraOffset.y, 0);//不处理Z

            SmoothDampFloat smoothCameraX = new SmoothDampFloat(_characterCameraTransform.position.x, smoothCameraXDuration);

            character.NotifyOnJump(character.SuperJumpAltitude, false);

            while (character.GamePosition.z < endGamePosition.z)
            {
                #region Calculate Character Z-position
                character.SetGamePosition(character.GamePosition + speed * TimeMgr.deltaTime * Vector3.forward);
                float normalizedPosition = (character.GamePosition.z - startGamePosition.z) / jumpDistance;
                Vector3 pivot = Route.GetPosition(character.x, character.GamePosition.z) + Vector3.up * jumpCurve.Evaluate(normalizedPosition) * jumpHeight;
                if (normalizedPosition <= fadeInPosition)
                {
                    pivot.y = Mathf.Lerp(startGamePosition.y, pivot.y, normalizedPosition / fadeInPosition);
                }

                if (normalizedPosition > hangTimePosition && !_reachedHangtime)
                {
                    NotifyHangtime();
                    _reachedHangtime = true;
                }

                character.SetGamePosition(pivot);
                _characterCamera.Target = pivot;
                _characterCamera.Position = pivot + cameraOffset;
                #endregion

                #region Camera Position
                Vector3 position = character.TransformPosition;
                smoothCameraX.Update();
                smoothCameraX.Target = position.x * .75f;

                float warpedCameraRatio = cameraCurve.Evaluate(normalizedPosition);

                Vector3 currentCameraOffset = Vector3.Lerp(startCameraOffset, cameraOffset, Mathf.SmoothStep(0f, 1f, normalizedPosition));
                Vector3 cameraPositionEnd = new Vector3(smoothCameraX.Value, pivot.y, character.TransformPosition.z) + currentCameraOffset;
                Vector3 cameraPositionNew = new Vector3(cameraPositionEnd.x, Mathf.Lerp(cameraPositionStart.y, cameraPositionEnd.y, warpedCameraRatio), cameraPositionEnd.z);
                cameraPositionNew.x = smoothCameraX.Value;
                Vector3 cameraTargetNew = Vector3.Lerp(cameraPositionNew - CameraTargetStartOffset, character.TransformPosition + (Vector3.up * cameraAimOffset), cameraAimFollowCurve
                .Evaluate(normalizedPosition));
                cameraTargetNew.x = smoothCameraX.Value;

                _characterCamera.Position = cameraPositionNew;
                _characterCamera.Target = cameraTargetNew;
                #endregion

                yield return null;
            }

            EndJump();
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
            if (_character.isStopRunning) return;
            if (PowerupMgr.Inst.IsInDevil)
            {
                switch (swipeDir)
                {
                    case SwipeDir.Up:
                        swipeDir = SwipeDir.Down;
                        break;
                    case SwipeDir.Down:
                        swipeDir = SwipeDir.Up;
                        break;
                    case SwipeDir.Left:
                        swipeDir = SwipeDir.Right;
                        break;
                    case SwipeDir.Right:
                        swipeDir = SwipeDir.Left;
                        break;
                    default:
                        break;
                }
            }

            float currentSpeed = _character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }
            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    _character.ChangeTrackBy(-1, characterChangeTrackLength / currentSpeed);
                    break;
                case SwipeDir.Right:
                    _character.ChangeTrackBy(+1, characterChangeTrackLength / currentSpeed);
                    break;
                case SwipeDir.Down:
                    EndJump();
                    _character.Roll();
                    break;
            }
        }

        private void EndJump()
        {
            isActive = false;
            this.isFromRunnerState = true;
            _characterController.detectCollisions = true;
            _game.CharacterState = _game.Running;
            _character.verticalSpeed = _character.CalculateJumpVerticalSpeed(finalJumpSpeed);
            _game.Modifiers.Resume();
            NotifyOnStop();
            _reachedHangtime = false;
        }

        private void NotifyOnStart()
        {
            if (OnStart != null)
            {
                OnStart();
            }
        }

        private void NotifyHangtime()
        {
            if (OnHangTime != null)
            {
                OnHangTime();
            }
        }

        private void NotifyOnStop()
        {
            if (OnStop != null)
            {
                OnStop();
            }
        }
    }
}