using System.Collections.Generic;
using UnityEngine;

namespace RG
{
    /// <summary>
    /// 角色状态管理器
    /// </summary>
    public class CharacterStateManager
    {
        private CharacterBase _character;
        private CharacterState _currentState;
        private Dictionary<System.Type, CharacterState> _states;

        public CharacterState CurrentState => _currentState;

        public CharacterStateManager(CharacterBase character)
        {
            _character = character;
            _states = new Dictionary<System.Type, CharacterState>();
        }

        /// <summary>
        /// 注册状态
        /// </summary>
        public void RegisterState<T>(T state) where T : CharacterState
        {
            var type = typeof(T);
            if (!_states.ContainsKey(type))
            {
                state.Initialize(_character);
                _states[type] = state;
            }
        }

        /// <summary>
        /// 切换到指定状态
        /// </summary>
        public bool ChangeState<T>() where T : CharacterState
        {
            var type = typeof(T);
            if (_states.TryGetValue(type, out var nextState))
            {
                if (_currentState != null)
                {
                    if (!_currentState.CanTransitionTo(nextState))
                    {
                        return false;
                    }
                    _currentState.Exit();
                }

                _currentState = nextState;
                _currentState.Enter();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 更新当前状态
        /// </summary>
        public void Update()
        {
            _currentState?.OnStateUpdate();
        }

        /// <summary>
        /// 物理更新当前状态
        /// </summary>
        public void FixedUpdate()
        {
            _currentState?.OnStateFixedUpdate();
        }
    }
} 