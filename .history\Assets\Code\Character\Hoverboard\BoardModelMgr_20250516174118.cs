using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using RG.Common;
using RG.Common.Logger;

namespace RG.Characters.Hoverboard
{
    public class BoardModelMgr : SingletonMonobehavior<BoardModelMgr>
    {
        [System.Serializable]
        public class HoverboardModelSetup
        {
            public string name;
            public string assetbundleName;
            public GameObject hoverboardPrefab;
        }

        [SerializeField]
        public HoverboardModelSetup[] hoverboards;

        [SerializeField]
        private AnimationClip speederSignature;

        public System.Action OnResetAnimator;
        public System.Action OnSpeederStart;
        public System.Action OnSpeederEnd;

        private Dictionary<string, HoverboardModelSetup> name2character = new Dictionary<string, HoverboardModelSetup>();

        [SerializeField]
        Material teleboardFXMaterial;
        private GameObject teleboardWarp;
        AnimationState teleboardAnimation;

        GliderAnimation gliderAnimation;
        readonly int id_Teleboard = Animator.StringToHash("Base Layer.HoverboardMenuTeleport");
        readonly int id_Glider = Animator.StringToHash("Base Layer.HoverboardMenuGlider");

        private GameObject _SuperJumperMenuEffectPre = null;
        private GameObject _SuperJumperMenuEffect = null;

        Animator animator;
        Vector3 intialScale = new Vector3(-1, -1, -1);
        Transform root;
        Material cachedModelMaterial;
        Material cachedCustomizationMaterial;
        Material[] cachedHeadMaterials;
        Renderer cachedHoverboard;
        Material cachedHoverboardMaterial = null;
        Material cachedSupersneakersMaterial;
        Renderer[] cachedCustomHoverboardModels = null;
        Material[] cachedCustomHoverboardModelMaterials = null;

        CharacterModel characterModel = null;

        bool cachedCustomizationState;

        public void Awake()
        {
            animator = gameObject.GetComponent<Animator>();
            foreach (HoverboardModelSetup hoverboardModelSetup in hoverboards)
            {
                name2character.Add(hoverboardModelSetup.name, hoverboardModelSetup);
            }
        }

        public string GetHoverboardAssetbundleName(string modelName)
        {
            HoverboardModelSetup hoverboardModel;
            if (name2character.TryGetValue(modelName, out hoverboardModel))
            {
                return hoverboardModel.assetbundleName;
            }

            return string.Empty;
        }

        public void ChangeHoverboard_RealPVP(RG.Board.Board board, GameObject hoverboardGO,
                                     CharacterModel cm, bool willUpdateAnimation, bool willAllowSignatureAnimation)
        {
            if (board == null)
            {
                RGLogger.LogError("Character", "  ChangeHoverboard_RealPVP Error ! No Find Board Config! ");
                return;
            }

            if (animator.GetCurrentAnimatorStateInfo(0).fullPathHash == id_Teleboard && willUpdateAnimation)
            {
                StopAllCoroutines();
                ResetAnimator();
                TeleboardEnd(0);
            }
            else if (animator.GetCurrentAnimatorStateInfo(0).fullPathHash == id_Glider)
            {
                ResetAnimator();
            }
            if (teleportFxActive)
            {
                ResetTeleportFX();
            }

            characterModel = cm;

            if (characterModel == null)
            {
                RGLogger.Log("Character", "HoverboardModelPreviewFactory - characterModel was null!");
                return;
            }

            HoverboardModelSetup hoverboardModel;
            if (name2character.TryGetValue(board.Model, out hoverboardModel))
            {
                if (name == "Jumpboard")
                {
                    characterModel.MeshSuperSneaker.enabled = true;
                }
                else
                {
                    characterModel.MeshSuperSneaker.enabled = false;
                }

                string boardName = "hoverboard";
                if (hoverboardModel.hoverboardPrefab == null)
                {
                    hoverboardModel.hoverboardPrefab = board.GetModelPrefab();
                }
                hoverboardGO = characterModel.SetNewHoverboard(hoverboardGO, hoverboardModel.hoverboardPrefab, boardName, board);
                cachedHoverboard = hoverboardGO.GetComponent<Renderer>();
                global::Hoverboard.Instance.ShowBoardEff(hoverboardGO, characterModel, board, true);
                if (name == "gliderboard")
                {
                    gliderAnimation = hoverboardGO.GetComponentInChildren<GliderAnimation>();
                }

                if (name == "teleboard")
                {
                    CachMaterials();
                }
                var csList = global::Hoverboard.Instance.GetActiveCustomSets(board);
                HoverboardSelection.CustomSet[] customSets = csList == null ? null : csList.ToArray();
                if (customSets != null)
                {
                    bool shouldShow = true;

                    if (shouldShow == customSets.Length > 0)
                    {
                        AddCustomModelsAndFX(customSets, 0, hoverboardGO.transform);
                    }
                    if (customSets.Length == 1 && willAllowSignatureAnimation)
                    {

                        if (customSets[0].ability != BoardAbility.None)
                        {
                            if (customSets[0].ability == BoardAbility.Teleport)
                            {
                                CachMaterials();
                            }
                            if (customSets[0].VanityMenuFxPrefab != null)
                            {
                                characterModel.AddCustomHoverboardVanityMenuFX(customSets[0].VanityMenuFxPrefab, hoverboardGO.transform);
                            }

                            if (customSets[0].ability == BoardAbility.Lowrider)
                            {
                                PlaySignatureMove(customSets[0].ability, null, true, customSets);
                            }
                            else
                            {
                                PlaySignatureMove(customSets[0].ability, null, false, customSets);
                            }
                        }
                    }
                    else
                    {
                        if (willUpdateAnimation)
                        {

                            bool useLowrider = false;
                            bool useGlider = false;

                            for (int i = 0; i < customSets.Length; i++)
                            {
                                if (i == 0)
                                {
                                    if (customSets[i].ability == BoardAbility.Lowrider)
                                    {
                                        useLowrider = true;
                                    }
                                }

                                if (i == 0 && customSets[i].ability == BoardAbility.Glider)
                                {
                                    useGlider = true;
                                }
                            }

                            {
                                characterModel.Animator.Controller.EnterPvpLoading();

                                if (useGlider)
                                {
                                    if (animator.GetCurrentAnimatorStateInfo(0).fullPathHash != id_Glider)
                                    {
                                        animator.Play(id_Glider);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        void AddCustomModelsAndFX(HoverboardSelection.CustomSet[] customSets, int i, Transform hoverboardRoot)
        {
            if (customSets.Length > i)
            {
                if (customSets[i].ability == BoardAbility.Teleport)
                {
                    CachMaterials();
                }

                if (customSets[i].baseModelPrefab != null)
                {
                    cachedHoverboard.enabled = false;

                    Renderer[] renderers = cachedHoverboard.GetComponentsInChildren<Renderer>();
                    for (int j = 0; j < renderers.Length; j++)
                    {
                        renderers[j].enabled = false;
                    }

                    cachedHoverboard = characterModel.AddCustomHoverboardModel(customSets[i].baseModelPrefab, true, hoverboardRoot);
                }

                characterModel.AddCustomHoverboardModel(customSets[i].modelPrefab, true, hoverboardRoot);

                if (customSets[i].ability == BoardAbility.Glider)
                {
                    gliderAnimation = hoverboardRoot.gameObject.GetComponentInChildren<GliderAnimation>();
                }
            }
        }

        public void PlaySignatureMove(BoardAbility ability, Animation characterAnimation, bool useLowrider, HoverboardSelection.CustomSet[] customSet)
        {
            RGLogger.Log("Character", "PlaySignatureMove() abil: " + ability + " anim: " + characterAnimation + " low: " + useLowrider);

            if (teleportFxActive)
            {
                ResetTeleportFX();
            }

            if (_SuperJumperMenuEffect != null)
            {
                _SuperJumperMenuEffect.gameObject.SetActive(false);
            }

            if (ability == BoardAbility.Glider)
            {
                if (animator.GetCurrentAnimatorStateInfo(0).fullPathHash != id_Glider)
                {
                    animator.Play(id_Glider);
                }
            }
            else if (ability == BoardAbility.HighSpeed || ability == BoardAbility.Speed)
            {
                StartCoroutine(SpeederEvents(speederSignature.length));
            }
            else if (ability == BoardAbility.Teleport)
            {
                root = characterModel.Hip;
                HoverboardSelection.CustomSet set = null;
                for (int i = 0; i < customSet.Length; i++)
                {
                    if (customSet[i].ability == BoardAbility.Teleport)
                    {
                        set = customSet[i];
                    }
                }

                if (animator.GetCurrentAnimatorStateInfo(0).fullPathHash != id_Teleboard)
                {
                    animator.Play(id_Teleboard);
                }
            }

            for (int i = 0; i < customSet.Length; i++)
            {
                if (customSet[i].ability == ability)
                {
                    if (_SuperJumperMenuEffectPre != customSet[i].AbilityMenuEffectFXPrefab
                        || _SuperJumperMenuEffect == null)
                    {
                        _SuperJumperMenuEffectPre = customSet[i].AbilityMenuEffectFXPrefab;
                        if (_SuperJumperMenuEffectPre != null)
                        {
                            _SuperJumperMenuEffect = Instantiate(_SuperJumperMenuEffectPre);
                            _SuperJumperMenuEffect.transform.parent = characterModel.CurrentHoverboard.transform;
                            _SuperJumperMenuEffect.transform.localPosition = Vector3.zero;
                            _SuperJumperMenuEffect.transform.localRotation = Quaternion.identity;
                            _SuperJumperMenuEffect.transform.localScale = Vector3.one;
                        }
                    }
                    if (_SuperJumperMenuEffect != null)
                    {
                        _SuperJumperMenuEffect.gameObject.SetActive(false);
                        _SuperJumperMenuEffect.gameObject.SetActive(true);
                    }
                    break;
                }
            }
        }

        IEnumerator SpeederEvents(float waitTime)
        {
            if (OnSpeederStart != null)
            {
                OnSpeederStart();
            }
            yield return new WaitForSeconds(waitTime);

            if (OnSpeederEnd != null)
            {
                OnSpeederEnd();
            }
        }

        bool teleportFxActive = false;

        void CachMaterials()
        {
            if (characterModel != null)
            {
                Material modelMaterial = characterModel.Model.sharedMaterial;
                if (modelMaterial != teleboardFXMaterial)
                    cachedModelMaterial = modelMaterial;

                Material[] headMaterials = characterModel.HeadModel.sharedMaterials;
                if (headMaterials[0] != teleboardFXMaterial)
                {
                    cachedHeadMaterials = headMaterials;
                }

                Material customizeModelMaterial = characterModel.OutfitModel.sharedMaterial;
                if (customizeModelMaterial != teleboardFXMaterial)
                    cachedCustomizationMaterial = customizeModelMaterial;

                cachedCustomizationState = characterModel.OutfitModel.enabled;
            }
        }

        void TeleboardEnd(float duration)
        {
            if (characterModel == null)
                return;

            characterModel.transform.localScale = intialScale;

            if (duration > 0)
            {
                teleboardWarp.SetActive(true);
                teleboardAnimation.normalizedTime = 1.0f;
                teleboardAnimation.weight = 1.0f;
                teleboardAnimation.speed = -1.8f;
                teleboardAnimation.enabled = true;
            }
            if (root != null)
                teleboardWarp.transform.position = root.position;

            StartCoroutine(Utility.SetInterval(teleboardAnimation.length, t =>
                    {
                        if (t == 1)
                        {
                            teleboardWarp.SetActive(false);
                            animator.StopPlayback();
                        }
                    }));

            StartCoroutine(Utility.SetInterval(duration, t =>
                    {
                        if (t == 1 && teleportFxActive)
                        {
                            ResetTeleportFX();
                        }
                    }));
        }

        public void ResetTeleportFX()
        {
            if (characterModel == null || !teleportFxActive)
            {
                return;
            }
            characterModel.Model.enabled = true;
            characterModel.HeadModel.enabled = true;
            if (cachedHoverboard != null)
            {
                cachedHoverboard.enabled = true;
            }

            SetEnabledWithinCurrentCustomHoverboardModels(true);
            characterModel.OutfitModel.enabled = cachedCustomizationState;
            if (characterModel.CurrentHoverboard != null)
            {
                characterModel.CurrentHoverboard.SetActive(true);
            }

            if (cachedModelMaterial != null)
            {
                characterModel.Model.GetComponent<Renderer>().material = cachedModelMaterial;
            }

            if (characterModel.HeadModel != null)
            {
                if (characterModel.HeadModel.materials != null)
                {
                    if (cachedHeadMaterials != null)
                    {
                        if (cachedHeadMaterials.Length > 0)
                        {
                            bool hasNull = false;
                            for (int i = 0; i < cachedHeadMaterials.Length; i++)
                            {
                                if (cachedHeadMaterials[i] == null)
                                {
                                    hasNull = true;
                                    break;
                                }
                            }
                            if (!hasNull)
                            {
                                characterModel.HeadModel.materials = cachedHeadMaterials;
                            }
                        }
                    }
                }
            }

            if (cachedCustomizationMaterial != null) characterModel.OutfitModel.material = cachedCustomizationMaterial;
            if (cachedSupersneakersMaterial != null) characterModel.MeshSuperSneaker.material = cachedSupersneakersMaterial;

            characterModel.StartEyeAnimations();

            if (cachedHoverboard != null)
                cachedHoverboard.material = cachedHoverboardMaterial;

            if (cachedCustomHoverboardModelMaterials != null)
            {
                if (cachedCustomHoverboardModelMaterials.Length > 0)
                {
                    for (int i = 0; i < characterModel.CurrentCustomBoardModels.Count; i++)
                    {
                        if (cachedCustomHoverboardModelMaterials.Length <= i)
                        {
                            break;
                        }

                        if (cachedCustomHoverboardModelMaterials[i] != null)
                        {
                            if (cachedCustomHoverboardModelMaterials.Length - 1 >= i &&
                            characterModel.CurrentCustomBoardModelsRenderes.Count > i)
                            {
                                characterModel.CurrentCustomBoardModelsRenderes[i].material = cachedCustomHoverboardModelMaterials[i];
                            }

                            if (cachedCustomHoverboardModels.Length - 1 >= i && cachedCustomHoverboardModels[i] != null)
                            {
                                cachedCustomHoverboardModels[i].material = cachedCustomHoverboardModelMaterials[i];
                            }
                        }
                    }
                }
            }

            characterModel.transform.localScale = intialScale;

            teleportFxActive = false;
        }

        void SetEnabledWithinCurrentCustomHoverboardModels(bool isEnabled)
        {
            if (cachedCustomHoverboardModels != null)
            {
                foreach (var element in cachedCustomHoverboardModels)
                {
                    if (element != null)
                    {
                        element.enabled = isEnabled;
                    }
                }
            }

            foreach (var element in characterModel.CharacterBody.CharacterPropRenderers)
            {
                if (element != null)
                {
                    element.enabled = isEnabled;
                }
            }
        }

        public void ResetAnimator()
        {
            if (animator != null)
                animator.Play("empty");
            if (OnResetAnimator != null)
                OnResetAnimator();
        }
    }
}
