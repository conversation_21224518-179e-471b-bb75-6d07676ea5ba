using UnityEngine;
using RG.Common;

namespace RG.Characters.Components
{
    public class CharacterJump : MonoBehaviour
    {
        private CharacterBase _character;
        private CharacterMovement _movement;
        private float _jumpHeight = 20f;
        private float _superJumpHeight = 40f;
        private bool _isJumping;
        private bool _isFalling;
        private bool _startedJumpFromGround;
        private float _trainJumpSampleZ;
        private bool _trainJump;

        public void Initialize(CharacterBase character, CharacterMovement movement)
        {
            _character = character;
            _movement = movement;
        }

        public bool CanJump()
        {
            bool forgivenJump = !_isJumping && _movement.GetVerticalSpeed() <= 0f && 
                               _movement.GetVerticalSpeed() > CharacterConstant.JUMP_VERTICAL_SPEED;
            return _character.CharacterController.isGrounded || forgivenJump;
        }

        public void PerformJump()
        {
            if (!CanJump()) return;

            _isJumping = true;
            _isFalling = false;
            _character.IsGrounded.Value = false;

            float jumpSpeed = CalculateJumpVerticalSpeed(_jumpHeight);
            _movement.SetVerticalSpeed(jumpSpeed);

            if (_character.IsJumpingHigher)
            {
                Vector3 position = _character.GamePosition;
                SuperSneakersJump jump = new SuperSneakersJump
                {
                    StartGamePositionZ = position.z,
                    Length = GetJumpDistance(_character.CurrentSpeed, _superJumpHeight) * _character.superSneakersJumpApexRatio,
                    EndGamePositionZ = position.z + GetJumpDistance(_character.CurrentSpeed, _superJumpHeight) * _character.superSneakersJumpApexRatio,
                    StartGamePositionY = position.y
                };
                _character._superSneakersJump = jump;
                _movement.SetVerticalSpeed(0f);
            }

            _character.NotifyOnJump(_character.IsJumpingHigher ? _superJumpHeight : _jumpHeight, false);

            if (_character.IsRunningOnGround())
            {
                _startedJumpFromGround = true;
                _trainJump = false;
                _trainJumpSampleZ = _character.GamePosition.z + CharacterConstant.JUMP_TRAIN_DISTANCE;
            }
        }

        public void UpdateJumpState()
        {
            if (_movement.GetVerticalSpeed() < 0f && _character.CharacterController.isGrounded)
            {
                if (_startedJumpFromGround && _trainJump && _character.IsRunningOnGround())
                {
                    _character.SendJumpOverTrainEvent();
                }

                if (!_character.IsRunningAir())
                {
                    _startedJumpFromGround = false;
                }

                _character.IsGrounded.Value = true;

                if (_isJumping || _isFalling)
                {
                    _character._jumpUpperCollision = false;
                    _character._jumpUpperCollisionEvaded = true;

                    _isJumping = false;
                    _isFalling = false;
                    _character.NotifyOnLanding();
                }

                _movement.SetVerticalSpeed(0f);
            }
            else
            {
                if (_startedJumpFromGround && _trainJumpSampleZ < _character.GamePosition.z)
                {
                    CheckTrainJump();
                    _trainJumpSampleZ += CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }
        }

        private void CheckTrainJump()
        {
            RaycastHit hit;
            if (Physics.Raycast(new Ray(_character.TransformPosition, -Vector3.up), out hit))
            {
                if (hit.collider.CompareTag("HitMovingTrain") ||
                    hit.collider.CompareTag("HitTrain"))
                {
                    _trainJump = true;
                }
            }
        }

        private float CalculateJumpVerticalSpeed(float jumpHeight)
        {
            return Mathf.Sqrt(2 * jumpHeight * _character.Gravity);
        }

        private float GetJumpDistance(float speed, float jumpHeight)
        {
            return speed * 2f * CalculateJumpVerticalSpeed(jumpHeight) / _character.Gravity;
        }
    }
} 