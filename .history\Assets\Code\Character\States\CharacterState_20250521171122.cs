using UnityEngine;
using System.Collections;
using SubwayPvp.Core.Camera;
using RG.Cameras;
using RG.Inputs;

namespace RG.Characters.States
{
    /// <summary>
    /// 角色状态基类
    /// Base class for character states
    /// </summary>
    public abstract class CharacterState : PvpBehaviour
    {
        [SerializeField] protected CameraData _cameraData = null;
        [SerializeField] protected bool _hasEnded = false;

        protected Game _game;
        protected Character _character;
        protected CharacterCamera _characterCamera;
        protected CharacterRendering _characterRendering;
        protected CharacterController _characterController;

        protected virtual void Awake()
        {
            _game = Game.Instance;
            _character = _game.Character;
            _characterCamera = _character.CharacterCamera;
            _characterRendering = _character.CharacterRendering;
            _characterController = _character.CharacterController;
        }

        protected virtual void Reset()
        {
            _cameraData = new CameraData();
        }

        public CameraData CameraData { get { return _cameraData; } }

        public virtual void HandleSwipe(SwipeDir swipeDir) { }

        public virtual IEnumerator Begin()
        {
            yield break;
        }

        public virtual IEnumerator End()
        {
            yield break;
        }

        public virtual void HandleCriticalHit() { }

        public virtual void HandleDoubleTap() { }

        /// <summary>
        /// 进入状态时调用
        /// Called when entering the state
        /// </summary>
        public virtual void Enter() { }

        /// <summary>
        /// 退出状态时调用
        /// Called when exiting the state
        /// </summary>
        public virtual void Exit() { }

        /// <summary>
        /// 每帧更新
        /// Called every frame
        /// </summary>
        public virtual void Update() { }

        /// <summary>
        /// FixedUpdate 更新，用于物理计算
        /// Called every fixed update, for physics calculations
        /// </summary>
        public virtual void FixedUpdate() { }

        /// <summary>
        /// 初始化状态
        /// Initializes the state
        /// </summary>
        /// <param name="character">角色引用</param>
        public void Initialize(CharacterBase character)
        {
            _character = character;
        }
    }
}