using UnityEngine;
using UnityEditor;
using RG.Characters.Config;

namespace RG.Characters.Config.Editor
{
    public class CreateDefaultConfigs : EditorWindow
    {
        [MenuItem("Game/Character/Create Default Configs")]
        public static void CreateConfigs()
        {
            // 创建基础配置
            var characterConfig = ScriptableObject.CreateInstance<CharacterConfig>();
            AssetDatabase.CreateAsset(characterConfig, "Assets/Resources/Config/CharacterConfig.asset");
            
            // 创建状态配置
            var stateConfig = ScriptableObject.CreateInstance<CharacterStateConfig>();
            AssetDatabase.CreateAsset(stateConfig, "Assets/Resources/Config/CharacterStateConfig.asset");
            
            // 设置默认值
            SetDefaultCharacterConfig(characterConfig);
            SetDefaultStateConfig(stateConfig);
            
            // 保存资源
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("默认配置已创建完成！");
        }
        
        private static void SetDefaultCharacterConfig(CharacterConfig config)
        {
            // 基础属性
            config.BaseSpeed = 30f;
            config.Gravity = 200f;
            
            // 跳跃相关
            config.JumpHeight = 20f;
            config.SuperJumpHeight = 40f;
            config.SuperJumpApexRatio = 0.5f;
            
            // 滑行相关
            config.GlideTime = 0.1f;
            config.GliderGravity = 0.01f;
            
            // 碰撞相关
            config.CharacterControllerHeight = 4f;
            config.CharacterControllerCenter = new Vector3(0, 2f, 0);
            config.CharacterTriggerHeight = 4f;
            config.CharacterTriggerCenter = new Vector3(0, 4f, 0);
            
            // 轨道相关
            config.InitialTrackIndex = 1;
            config.TrackChangeDuration = 0.3f;
            
            // 网络同步
            config.PositionSyncFrequency = 0.1f;
            config.StateSyncFrequency = 0.2f;
            
            // 性能优化
            config.EnableObjectPool = true;
            config.ObjectPoolInitialSize = 10;
            config.ObjectPoolMaxSize = 50;
        }
        
        private static void SetDefaultStateConfig(CharacterStateConfig config)
        {
            // 状态转换配置
            config.StateTransitions = new System.Collections.Generic.List<CharacterStateConfig.StateTransitionConfig>
            {
                new CharacterStateConfig.StateTransitionConfig
                {
                    FromState = "Running",
                    ToState = "Jumping",
                    Condition = "IsGrounded && Input.GetKeyDown(KeyCode.Space)",
                    Priority = 1
                },
                new CharacterStateConfig.StateTransitionConfig
                {
                    FromState = "Jumping",
                    ToState = "Falling",
                    Condition = "verticalSpeed < 0",
                    Priority = 1
                },
                new CharacterStateConfig.StateTransitionConfig
                {
                    FromState = "Falling",
                    ToState = "Running",
                    Condition = "IsGrounded",
                    Priority = 1
                },
                new CharacterStateConfig.StateTransitionConfig
                {
                    FromState = "Running",
                    ToState = "Rolling",
                    Condition = "Input.GetKeyDown(KeyCode.DownArrow)",
                    Priority = 2
                },
                new CharacterStateConfig.StateTransitionConfig
                {
                    FromState = "Rolling",
                    ToState = "Running",
                    Condition = "!IsRolling",
                    Priority = 1
                }
            };
            
            // 状态行为配置
            config.StateBehaviors = new System.Collections.Generic.List<CharacterStateConfig.StateBehaviorConfig>
            {
                new CharacterStateConfig.StateBehaviorConfig
                {
                    StateName = "Running",
                    AnimationName = "Run",
                    Duration = 0f,
                    CanBeInterrupted = true,
                    Effects = new string[] { "RunDust" }
                },
                new CharacterStateConfig.StateBehaviorConfig
                {
                    StateName = "Jumping",
                    AnimationName = "Jump",
                    Duration = 0.5f,
                    CanBeInterrupted = false,
                    Effects = new string[] { "JumpDust" }
                },
                new CharacterStateConfig.StateBehaviorConfig
                {
                    StateName = "Falling",
                    AnimationName = "Fall",
                    Duration = 0f,
                    CanBeInterrupted = false,
                    Effects = new string[] { }
                },
                new CharacterStateConfig.StateBehaviorConfig
                {
                    StateName = "Rolling",
                    AnimationName = "Roll",
                    Duration = 0.5f,
                    CanBeInterrupted = false,
                    Effects = new string[] { "RollDust" }
                }
            };
            
            // 状态参数
            config.GroundCheckHeight = 0.1f;
            config.DoubleJumpCooldown = 0.5f;
            config.RollDuration = 0.5f;
            config.GlideDuration = 1.0f;
            
            // 状态事件
            config.StateEnterEvents = new string[] { "OnStateEnter" };
            config.StateExitEvents = new string[] { "OnStateExit" };
            config.StateUpdateEvents = new string[] { "OnStateUpdate" };
        }
    }
} 