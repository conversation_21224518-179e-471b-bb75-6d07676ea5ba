﻿using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using RG.Routes;
using RG.Characters;
using Common = RG.Common;
using System.Linq;
using System;
using Board = RG.Board;
using CharacterData = RG.CharacterData;
using RG;
using RG.Localization;
using RG.Common.Logger;

public partial class PvpDirector : MonoBehaviour
{
    public const string ConnectToMasterFailed = "ConnectToMasterFailed";

    private bool m_isPlaying = false;


    private Dictionary<int, byte[]> m_initParams = null;

    private void InitModuleLogic()
    {
        //NetDirector.OnPlayerConnected       = OnPlayerConnected;
        NetDirector.OnPlayerDisconnected += OnPlayerDisconnected;
        NetDirector.OnActJoinedRoom += OnActJoinedRoom;

        NetDirector.OnDisconnected += OnDisconnected;
        NetDirector.OnRuleEvent += OnRuleEvent;
        NetDirector.OnConnectFaile += OnConnectFaile;
        NetDirector.OnJoinRoomFaile += OnJoinRoomFaile;
        NetDirector.OnActJoinedRoom += OnJoinRoomSuccess;
    }

    public void FreeMoudle()
    {
        NetDirector.OnPlayerDisconnected -= OnPlayerDisconnected;
        NetDirector.OnActJoinedRoom -= OnActJoinedRoom;

        NetDirector.OnDisconnected -= OnDisconnected;
        NetDirector.OnRuleEvent -= OnRuleEvent;
    }

    public void Free()
    {
        m_isPlaying = false;

        NetDirector.LeaveRoom();
    }


    #region 等待中,其他玩家加入
    private void OnPlayerEnter(int _senderId, byte[] _initPms)
    {
        if (_senderId == NetDirector.PlayerID)
            return;

        //Package_InitParams initPms = SerializationUnit.DeserializeObject<Package_InitParams>(_initPms);

        RGLogger.Log("Pvp", "接受到玩家:" + _senderId + " 的初始化数据包");
    }
    #endregion

    #region 其他玩家离开
    private void OnPlayerDisconnected(int _playerId)
    {
        if (m_isPlaying && !PvpMgr.Inst.CheckPlayerFinish(_playerId))
        {
            Robot role = RobotMgr.Instance.GetRobotById(_playerId);
            if (null != role)
            {
                role.DestroyPhotonNetWork();
                role.ChangeState(RobotStateType.End);
                PvpPlayer player = PvpMgr.Inst.Players.Find(it => it.Uid == role.UID);
                if (player != null)
                {
                    var warningString = string.Format("{0} {1}", player.NickName, LocalizationMgr.Inst.Get("PopUp.Offline"));
                    RG.UIToast.ShowNormal(warningString);
                }
            }
        }
        else
        {
            //RealPVPMatchPopUp.Instance.PlayerLeave(_playerId);
        }
    }
    #endregion

    #region 退出房间
    public void LeaveBattle()
    {
        RG.Characters.Character.Instance.DestroyPhotonNetWork();

        Free();
    }
    #endregion

    #region 同步初始化参数
    private void OnActJoinedRoom()
    {
        //道具赛携带宝物列表
        string slotContent = string.Empty;
        NetDirector.Rule_PlayerInitInfo(RG.XPluginAccount._userid, NetDirector.PlayerID, GenInitParams(), RG.XPluginUtility.ChannelId, SystemInfo.deviceUniqueIdentifier, slotContent);
    }

    #endregion

    #region 匹配模块
    /// <summary>
    /// 点击匹配对战
    /// </summary>
    private NetDirector.tRoomType m_roomType;
    public NetDirector.tRoomType roomType { get { return m_roomType; } }

    private string m_roomName;
    public string RoomName
    {
        get { return m_roomName; }
    }

    private int DisconnectTimestamp;
    private Common.Timer _Timer = new Common.Timer(10);
    public List<int> RoundTripTimeList = new List<int>();

    public void SetPvpInfo(NetDirector.tRoomType roomType, string _roomName = null)
    {
        m_roomType = roomType;
        m_roomName = _roomName;
    }

    public void OnClickPvp(NetDirector.tRoomType roomType, string _roomName = null)
    {
        //"开始连接对战服务器!"
        RobotMgr.Instance.ClearRobots();

        m_initParams = null;
        SetPvpInfo(roomType, _roomName);
        this._canReConnect = false;
        DisconnectTimestamp = 0;
        Do(NetDirector.EnterPVP(roomType, _roomName, (NetDirector.tEnterReturnCode code, object obj) =>
        {
            var extDict = GetExtDict();
            switch (code)
            {
                case NetDirector.tEnterReturnCode.kWaitForPlayers:
                    {
                        //"开始对战!"
                        // 确保角色已完全初始化后再启动游戏
                        EnsureCharacterInitializedAndStartGame();
                        m_isPlaying = true;
                        NetDirector.Shared.InPvpRoom();
                        DlogStart();
                    }
                    break;
                case NetDirector.tEnterReturnCode.kMatchSucceed:
                    {
                        StopDelayQuitRoom();
                        ReJoinActorID = NetDirector.PlayerID;
                        SetCanReConnect();
                        this._reConnecting = false;
                        //"正在搭建游戏世界，请勿离开界面!"
                    }
                    break;
                case NetDirector.tEnterReturnCode.kConnectSucceed:
                    {
                        RGLogger.Log("Pvp", "kConnectSucceed");
                        //"连接成功，请勿离开界面!"
                        if (DisconnectTimestamp > 0)
                        {
                            var time = ServerTimeManager.instance.ServerTime - DisconnectTimestamp;
                            RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.Photon, "ReConnect", time.ToString(), Common.Json.Serialize(extDict));
                            DisconnectTimestamp = 0;
                        }

                        RoundTripTimeList.Clear();
                        _Timer.Stop();
                        _Timer.intervalElapsed -= OnTimer;
                        _Timer.intervalElapsed += OnTimer;
                        _Timer.Start();

                        Game.OnEnd -= OnRealPvpGameEnded;
                        Game.OnEnd += OnRealPvpGameEnded;
                    }
                    break;

                case NetDirector.tEnterReturnCode.kSynBattle:
                    {
                        Game.Instance.ChangeState(GameStateType.Ready);
                    }
                    break;

                case NetDirector.tEnterReturnCode.kMatchTimeOut:
                    {
                        //"匹配超时"
                        QuitRoom();
                        RG.UIToast.ShowNormal(LocalizationMgr.Inst.Get("Network.TimeOut"));
                    }
                    break;

                case NetDirector.tEnterReturnCode.kConnectToMasterFailed:
                    {
                        //"与对战服务器连接失败"
                        RG.UIToast.ShowNormal(LocalizationMgr.Inst.Get("ConnectToMaster.Fail"));
                        QuitRoom();
                        RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.Photon, "ConnectToMasterFailed", Enum.GetName(typeof(NetDirector.tEnterReturnCode), code), Common.Json.Serialize(extDict));
                    }
                    break;
                case NetDirector.tEnterReturnCode.kMatchFailed:
                    {
                        //"网络异常，\n请检查网络！"
                        QuitRoom();
                        RG.UIToast.ShowNormal(LocalizationMgr.Inst.Get("Network.MatchFailed"));
                    }
                    break;
                case NetDirector.tEnterReturnCode.kResLoad:
                    {
                        RGLogger.Log("Pvp", "开始初始化资源");
                        ResourceLoad();
                        if (NetDirector.Shared.photonView.viewID <= 0)
                        {
                            NetDirector.Shared.photonView.viewID = 1;
                        }
                        RGLogger.Log("Pvp", "资源初始化完毕");
                    }
                    break;

                default:
                    RGLogger.Log("Pvp", code.ToString());
                    break;
            }
        }));
    }

    private void DlogStart()
    {
        Dictionary<string, object> ext_map = new Dictionary<string, object>();
        //房间ID
        ext_map.Add("roomId", PvpMgr.Inst.RoomID);
        //真人数量
        ext_map.Add("playerCount", PvpMgr.Inst.Players.Count(a => !PvpMgr.Inst.AIIds.Contains(a.Uid)));
        //玩家选择的滑板ID
        ext_map.Add("boardId", Board.Manager.Inst.Current.itemsId);
        //玩家选择的滑板等级
        ext_map.Add("boardLevel", Board.Manager.Inst.Current.Level);
        //玩家使用的角色ID
        ext_map.Add("characterId", CharacterData.Manager.Inst.currentCharacter.itemsId);
        //玩家的当前匹配分
        ext_map.Add("userIntegral", PvpMgr.Inst.UserIntegral);
        //玩家的匹配分变动值
        ext_map.Add("userVariableValue", PvpMgr.Inst.UserVariableValue);
        //结算名次
        //ext_map.Add("resultIndex",PvpMgr.Inst.UserVariableValue);
        //完赛时间
        //ext_map.Add("runTime", RG.Characters.Character.Instance.runningTime);
        //获得的奖励信息 （箱子ID或碎片）
        //ext_map.Add("chestSlotsInfo", RG.Characters.Character.Instance.runningTime);

        RG.XPluginAnalytics.PlayModeFlow(RG.PlayModeStatus.Start, RG.PlayModeType.Pvp,
            RG.PlayModeId.Pvp, RG.Themes.ThemeManager.Instance.Theme.Id, Common.Utility.NativeTimeStamp().ToString(),
            RG.PlayModeFailReason.None, Common.Json.Serialize(ext_map));
    }

    private void OnRealPvpGameEnded()
    {
        _Timer.intervalElapsed -= OnTimer;
        var avgRoundTripTime = RoundTripTimeList.Count > 0 ? RoundTripTimeList.Average() : 0;
        var maxRoundTripTime = RoundTripTimeList.Count > 0 ? RoundTripTimeList.Max() : 0;
        var extDict = GetExtDict();
        var eventParamValue = $"{Math.Round(avgRoundTripTime)},{maxRoundTripTime}";
        RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.Photon, "PhotonEnd", eventParamValue, Common.Json.Serialize(extDict));
    }

    private void OnTimer()
    {
        try
        {
            var extDict = GetExtDict();
            var serverTime = ServerTimeManager.instance.ServerTime;
            var roundTripTime = extDict["RoundTripTime"];
            RoundTripTimeList.Add(int.Parse(roundTripTime));
            RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.Photon, "RoundTripTime", roundTripTime, Common.Json.Serialize(extDict));

            // 使用服务器时间作为基准
            if (serverTime <= 0)
            {
                RGLogger.LogError("Pvp", "服务器时间异常，等待重连...");
                OnDisconnected();
                return;
            }

            var actTime = double.Parse(roundTripTime);
            var timeDiff = actTime - serverTime;

            // 如果时间差过大，重新同步
            if (Math.Abs(timeDiff) > 1000) // 1000秒阈值
            {
                RGLogger.LogError("Pvp", $"时间同步异常，差值: {timeDiff}秒，重新连接...");
                OnDisconnected();
                return;
            }

            RGLogger.Log("Pvp", $"触发时间在:{timeDiff}秒后  帧数:{PvpDirector.PhotonTimeToFmIdx(actTime)} 当前帧:{TimeMgr.frameCount}");
        }
        catch (Exception e)
        {
            RGLogger.LogError("Pvp", $"OnTimer 异常: {e.Message}");
            OnDisconnected();
        }
    }

    private Dictionary<string, string> GetExtDict()
    {
        int realPlayerNum = 0;
        foreach (var player in PvpMgr.Inst.Players)
        {
            if (PvpMgr.Inst.AIIds.Contains(player.Uid) == false)
            {
                realPlayerNum++;
            }
        }
        var extDict = new Dictionary<string, string>
            {
                { "RoomId",PvpMgr.Inst.RoomID },
                { "RandomIndex",PvpMgr.Inst.RandomIndex.ToString() },
                { "RealPlayerNum", realPlayerNum.ToString() },
                { "RoundTripTime", ServerTimeManager.instance.ServerTime.ToString() },
                { "GamePlay", LocalizationMgr.Inst.Get(RG.GamePlayMgr.Inst.CurrentGamePlay.Name) }
            };
        return extDict;
    }

    /// <summary>
    /// 生成同步数据结构体
    /// </summary>
    /// <returns></returns>
    private byte[] GenInitParams()
    {
        Package_InitParams _initPms = new Package_InitParams();

        _initPms.m_assetPlayer = new Package_PlayerCharacter();
        _initPms.Request();

        return SerializationUnit.SerializeObject(_initPms);
    }

    /// <summary>
    /// 匹配成功,初始化房间各个玩家数据
    /// </summary>
    /// <param name="_initParams"></param>
    private void ResourceLoad()
    {
        try
        {
            RobotMgr.Instance.ClearRobots();

            // 检查必要组件
            if (!ValidateRequiredComponents())
            {
                return;
            }

            // 初始化角色
            InitializeCharacter();

            // 初始化AI
            InitializeAI();

            // 初始化其他玩家
            InitializeOtherPlayers();

            m_initParams.Clear();
        }
        catch (Exception e)
        {
            RGLogger.LogError("Pvp", $"ResourceLoad 异常: {e.Message}");
            LeaveBattle();
            PvpMgr.Inst.BackToMainMenu();
        }
    }

    /// <summary>
    /// 确保角色完全初始化后再启动游戏
    /// </summary>
    private void EnsureCharacterInitializedAndStartGame()
    {
        try
        {
            // 确保 Character.Instance 存在且已初始化
            if (RG.Characters.Character.Instance == null)
            {
                RGLogger.LogError("Pvp", "Character.Instance 为 null，无法启动PVP游戏");
                LeaveBattle();
                PvpMgr.Inst.BackToMainMenu();
                return;
            }

            // 确保角色已完全初始化
            if (!RG.Characters.Character.Instance.IsInitialized)
            {
                RGLogger.Log("Pvp", "角色未初始化，正在初始化...");
                RG.Characters.Character.Instance.Initialize();
            }

            // 确保状态管理器已初始化
            if (RG.Characters.Character.Instance.CharacterStateManager == null)
            {
                RGLogger.LogError("Pvp", "CharacterStateManager 未初始化，无法启动PVP游戏");
                LeaveBattle();
                PvpMgr.Inst.BackToMainMenu();
                return;
            }

            // 启动游戏
            Game.Instance.StartGame();
            RGLogger.Log("Pvp", "PVP游戏启动成功");
        }
        catch (System.Exception e)
        {
            RGLogger.LogError("Pvp", $"启动PVP游戏时发生异常: {e.Message}\n{e.StackTrace}");
            LeaveBattle();
            PvpMgr.Inst.BackToMainMenu();
        }
    }

    private bool ValidateRequiredComponents()
    {
        if (Game.Instance == null)
        {
            RGLogger.LogError("Pvp", "Game.Instance 为 null");
            return false;
        }

        if (Game.Instance.Character == null)
        {
            RGLogger.LogError("Pvp", "Game.Instance.Character 为 null");
            return false;
        }

        if (Game.Instance.Character.CharacterModel == null)
        {
            RGLogger.LogError("Pvp", "Game.Instance.Character.CharacterModel 为 null");
            return false;
        }

        return true;
    }

    private void InitializeCharacter()
    {
        if (!m_initParams.ContainsKey(NetDirector.PlayerID))
        {
            RGLogger.LogError("Pvp", "m_initParams 中未找到当前玩家ID");
            LeaveBattle();
            PvpMgr.Inst.BackToMainMenu();
            return;
        }

        Game.Instance.Character.CharacterModel.ChangeCharacterModel(
            CharacterData.Manager.Inst.currentCharacterId,
            CharacterData.Manager.Inst.currentThemeIndex,
            false, true, false, true);

        RG.Routes.RouteManager.Instance.ReStart(RouteType.PVP);
    }

    private void InitializeAI()
    {
        if (PvpDirector.Shared.roomType == NetDirector.tRoomType.KP1)
        {
            int cIndex = 1;
            List<PvpPlayer> players = PvpMgr.Inst.Players;
            for (int i = 0, count = players.Count; i < count; i++)
            {
                PvpPlayer player = players[i];
                bool isAI = PvpMgr.Inst.AIIds.Contains(player.Uid);
                if (isAI)
                {
                    Robot dummy = RobotMgr.Instance.GetCreateRobot(RobotMgr.AIId * cIndex, player, true);
                    dummy.AI.InitLevel(player.AiLevel);
                    cIndex++;
                }
            }
        }
    }

    private void InitializeOtherPlayers()
    {
        foreach (KeyValuePair<int, byte[]> pair in m_initParams)
        {
            if (pair.Key == NetDirector.PlayerID)
            {
                Package_InitParams initPms = SerializationUnit.DeserializeObject<Package_InitParams>(pair.Value);
                Package_PlayerCharacter characterPm = initPms.m_assetPlayer;

                RG.Characters.Character.Instance.OpenPhotonNetWork(
                    characterPm.m_assetData.m_viewId,
                    characterPm.m_assetData.m_ownPlayerId);
                RG.Characters.Character.Instance.CharacterModel.IsVisibleShadow = true;
            }
            else
            {
                PvpPlayer player = PvpMgr.Inst.Players.Find(it => it.Uid == NetDirector.PlayerIDToUserID(pair.Key));
                if (player != null)
                {
                    Package_InitParams initPms = SerializationUnit.DeserializeObject<Package_InitParams>(pair.Value);
                    Package_PlayerCharacter characterPm = initPms.m_assetPlayer;

                    Robot dummy = RobotMgr.Instance.GetCreateRobot(pair.Key, player, false);
                    dummy.EffectList = player.EffectList;
                    dummy.OpenPhotonNetWork(characterPm.m_assetData.m_viewId, characterPm.m_assetData.m_ownPlayerId);
                }
            }
        }
    }

    #endregion

    #region 与服务器断开连接
    public void OnDisconnected()
    {
        RGLogger.Log("Pvp", "game OnDisconnected");
        if (this._reConnecting == true)
        {
            return;
        }
        if (NetDirector.Shared.inPvpRoom)
        {
            if (_canReConnect && Character.Instance.GamePosition.z < PvpMgr.ROUTE_LENGTH)
            {
                RG.XPluginAnalytics.CustomFlow(RG.XPluginEventId.Photon, "Disconnect", PhotonNetwork.networkingPeer.RoundTripTime.ToString(), Common.Json.Serialize(GetExtDict()));
                ReConnectStart();
            }
        }
        if (this._reConnecting == false)
        {
            DisConnectProgress();
        }
    }
    #endregion

    #region 规则
    public void OnRuleEvent(NetDirector.RuleType _actionCode, Dictionary<byte, object> _params)
    {
        switch (_actionCode)
        {
            case NetDirector.RuleType.kInit:
                {
                    m_initParams = (Dictionary<int, byte[]>)_params[1];
                    NetDirector.GameStartTime = (double)_params[2];

                    NetDirector.Shared.JoinRoomSucceed();
                }
                break;
            case NetDirector.RuleType.kMonsterCatch:
                {
                }
                break;
            case NetDirector.RuleType.kAddHealth:
                {
                }
                break;
            case NetDirector.RuleType.kEscape:
                {
                }
                break;
            case NetDirector.RuleType.kMonsterWin:
                {
                }
                break;
            case NetDirector.RuleType.kDisconnectCamp:
                {
                    var camp = (int)_params[1];
                    if (camp == 1)
                    {
                        NetDirector.Post_GetRankIdx(RG.XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position.z, RG.XPluginAccount._userid);
                    }
                }
                break;
            case NetDirector.RuleType.kHoverBoardSkillRoomMsg:
                {
                    double actTime = (double)_params[2];
                    bool canUseSkill = ((int)_params[3]) == 1;

                    if (!canUseSkill) return;

                    Package_UseSkill package = SerializationUnit.DeserializeObject<Package_UseSkill>((byte[])_params[1]);
                    package.m_triggerFpsSender = package.m_triggerFps;//缓存发送者触发帧，触发性道具（如：香蕉皮）
                    package.m_triggerFps = (int)PvpDirector.PhotonTimeToFmIdx(actTime);
                    var frameCount = TimeMgr.frameCount;
                    PowerupMgr.Inst.ApplyPropSkill(package, frameCount);


                }
                break;

            case NetDirector.RuleType.kUseItemRoomMsg:
                {
                    double actTime = (double)_params[2];

                    Package_UseSkill package = SerializationUnit.DeserializeObject<Package_UseSkill>((byte[])_params[1]);
                    package.m_triggerFpsSender = package.m_triggerFps;//缓存发送者触发帧，触发性道具（如：香蕉皮）
                    package.m_triggerFps = (int)PvpDirector.PhotonTimeToFmIdx(actTime);
#if UNITY_EDITOR
                    if (Common.GameConfig.isAIGame)
                    {
                        package.m_triggerFps = package.m_triggerFpsSender;
                    }
                    RGLogger.Log("Pvp", $"触发时间在:{actTime - PhotonNetwork.time}秒后  帧数:{PvpDirector.PhotonTimeToFmIdx(actTime)} 当前帧:{TimeMgr.frameCount}");
#endif
                    var frameCount = TimeMgr.frameCount;
                    PowerupMgr.Inst.ApplyPropSkill(package, frameCount);
                }
                break;

            case NetDirector.RuleType.kTimerAction:
                break;
            case NetDirector.RuleType.kRefClientServerTime:
                PhotonNetwork.FetchServerTimestamp();
                break;
            case NetDirector.RuleType.kEventPlayerCheat:
                {
                    var playerId = (string)_params[1];
                    var player = PvpMgr.Inst.Players.Find(it => it.Uid == playerId);
                    if (player != null)
                    {
                        //string.Format("{0}作弊，\n被踢出房间！", player.nickName)));
                        if (playerId == RG.XPluginAccount._userid)
                        {
                            this._canReConnect = false;
                            PhotonNetwork.Disconnect();
                        }
                    }
                }
                break;
            case NetDirector.RuleType.KIncValueByTeamer:
                PowerupMgr.Inst.FullEnergy();

                break;
            case NetDirector.RuleType.kBoardcastObViewer:
                break;
            case NetDirector.RuleType.kFinished:
                {
                    var postTime = (double)_params[3];
                    var endtime = (double)_params[2];
                    var playerId = (int)_params[1];
                    var userId = (string)_params[4];
                    PvpMgr.Inst.OnEndGame(playerId, endtime, postTime, userId);
                }
                break;
        }
    }
    #endregion

    #region 观战

    /// <summary>
    /// 玩家队列顺序
    /// </summary>
    /// <param name="_playerId"></param>
    /// <returns></returns>
    public int PlayerIndexAtList(int _playerId)
    {
        var playerList = new List<int>();
        for (int i = 0; i < PhotonNetwork.playerList.Length; i++)
        {
            var photonPlayer = PhotonNetwork.playerList[i];
            if (photonPlayer != null)
            {
                playerList.Add(photonPlayer.ID);
            }
        }
        for (int j = 0; j < playerList.Count; j++)
        {
            if (playerList[j] == _playerId)
            {
                return j;
            }
        }
        return -1;
    }
    #endregion
    #region 断线重连
    /// <summary>
    /// 断线后，服务端等待5s重连时间
    /// </summary>
    public const int PLAYER_TTL = 3000;
    /// <summary>
    /// 用于断线后重连的PlayerID
    /// </summary>
    /// <value></value>
    public int ReJoinActorID { get; private set; }
    /// <summary>
    /// 是否可以重连（主动断开不发生重连）
    /// </summary>
    private bool _canReConnect { get; set; }
    /// <summary>
    /// 是否正在断线重连
    /// </summary> <
    /// <value></value>
    private bool _reConnecting;
    public bool IsReConnecting => _reConnecting;
    /// <summary>
    /// 尝试重连次数
    /// </summary>
    private float _tryConnectNumbers;
    private const int TryConnectNumbers = 5;
    /// <summary>
    /// 重连间隔时间
    /// </summary>
    private float _connectIntervateTime = 1.0f;
    /// <summary>
    /// 重连超时处理
    /// </summary>
    private Coroutine _coReConnectTimeout;
    private Coroutine _codelayQuitRoom;
    private double _lastSyncTime;
    /// <summary>
    /// 开始重连
    /// </summary>
    private void ReConnectStart()
    {
        RGLogger.LogWarning("Pvp", "reconnectStart");
        _reConnecting = true;
        DisconnectTimestamp = ServerTimeManager.instance.ServerTime;

        // 重置时间相关状态
        RoundTripTimeList.Clear();
        _lastSyncTime = 0;

        StartCoroutine(ReConnecting(0.0f));
        if (_coReConnectTimeout != null)
        {
            StopCoroutine(_coReConnectTimeout);
            _coReConnectTimeout = null;
        }
        _coReConnectTimeout = StartCoroutine(ReConnectTimeout());
    }
    /// <summary>
    /// 重连中
    /// </summary>
    private IEnumerator ReConnecting(float WaitForSeconds)
    {
        yield return new WaitForSeconds(WaitForSeconds);
        _tryConnectNumbers++;
        PhotonNetwork.ReconnectAndRejoin();
    }

    /// <summary>
    /// 连接失败（尝试重连）
    /// </summary>
    private void OnConnectFaile(DisconnectCause cause)
    {
        RGLogger.LogWarning("Pvp", $"reconnect faile....{cause.ToString()},numbers:{this._tryConnectNumbers}");
        if (this._reConnecting)
        {
            if (this._tryConnectNumbers < TryConnectNumbers)
            {
                StartCoroutine(ReConnecting(_connectIntervateTime));
            }
            else
            {
                ReConnectFaile();
            }
        }
        else
        {
            RGLogger.LogWarning("Pvp", $"OnConnectFaile:{cause.ToString()}");
        }
    }
    /// <summary>
    /// 重连加入房间失败(不再尝试重连)
    /// </summary>
    private void OnJoinRoomFaile()
    {
        RGLogger.LogWarning("Pvp", "reconnect OnJoinRoomFaile");
        if (this._reConnecting)
        {
            ReConnectFaile();
        }
    }
    /// <summary>
    /// 重连加入房间成功
    /// </summary>
    private void OnJoinRoomSuccess()
    {
        RGLogger.LogWarning("Pvp", "reconnect OnJoinRoomSuccess");
        if (this._reConnecting)
        {
            ResetReConnect();
            this._canReConnect = true;
            PowerupMgr.Inst.IsUsePropRequesting = false;
            if (Character.Instance.GamePosition.z >= PvpMgr.ROUTE_LENGTH)
            {
                NetDirector.Rule_PostZ(RG.XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position);
                NetDirector.PostRoom_EndGame(10, PhotonNetwork.player.ID, RG.XPluginAccount._userid, true);
                NetDirector.Post_GetRankIdx(RG.XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position.z, RG.XPluginAccount._userid);
            }
        }
    }
    /// <summary>
    /// 重连失败处理
    /// </summary>
    private void ReConnectFaile()
    {
        ResetReConnect();
        DisConnectProgress();
    }
    /// <summary>
    /// 重置重连参数
    /// </summary>
    private void ResetReConnect()
    {
        if (_coReConnectTimeout != null)
        {
            StopCoroutine(_coReConnectTimeout);
            _coReConnectTimeout = null;
        }
        ResetConnectParams();
    }
    private void ResetConnectParams()
    {
        this._reConnecting = false;
        this._tryConnectNumbers = 0;
        this._canReConnect = false;
    }
    /// <summary>
    /// 掉线或重连失败后处理
    /// </summary>
    private void DisConnectProgress()
    {
        if (NetDirector.Shared.inPvpRoom)
        {
            //"网络异常，\n请检查网络！"
        }
        if (!m_isPlaying)
        {
            PvpDirector.Shared.LeaveBattle();
        }
        else if (NetDirector.shared.inPvpRoom)
        {
            Game.Instance.ChangeState(GameStateType.Disconnect);

        }
    }
    /// <summary>
    /// 重连超时处理
    /// </summary>
    /// <returns></returns>
    private IEnumerator ReConnectTimeout()
    {
        yield return new WaitForSeconds(GetPlayerTTLE() / 1000);
        if (this._reConnecting)
        {
            RGLogger.LogWarning("Pvp", "reconnect timeout");
            ResetConnectParams();
            DisConnectProgress();
        }
    }
    /// <summary>
    /// 设置是否可以断线重连（改成方法）
    /// </summary>
    private void SetCanReConnect()
    {
        this._canReConnect = true;
    }
    /// <summary>
    /// 获取playerttl
    /// </summary>
    /// <returns></returns>
    public int GetPlayerTTLE()
    {
        return PLAYER_TTL;
    }

    /// <summary>
    /// 连接photon失败，延时回到主界面
    /// </summary>
    /// <returns></returns>
    private void QuitRoom()
    {
        DelayQuitRoom(10.0f);
    }
    private void DelayQuitRoom(float delays)
    {
        StopDelayQuitRoom();
        _codelayQuitRoom = StartCoroutine(_DelayQuitRoom(delays));
        _Timer.intervalElapsed -= OnTimer;
    }

    private IEnumerator _DelayQuitRoom(float delays)
    {
        yield return new WaitForSeconds(delays);
        RGLogger.Log("Pvp", "_DelayQuitRoom()");
        RG.EventMgr.Instance.send(ConnectToMasterFailed);
    }
    private void StopDelayQuitRoom()
    {
        if (_codelayQuitRoom != null)
        {
            StopCoroutine(_codelayQuitRoom);
            _codelayQuitRoom = null;
        }
    }

    #endregion
}
