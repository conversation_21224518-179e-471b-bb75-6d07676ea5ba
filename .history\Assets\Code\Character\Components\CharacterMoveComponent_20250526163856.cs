using UnityEngine;
using RG.Characters;
using RG.Routes;
using RG.Level;
using System.Collections;
using System.Collections.Generic;

namespace RG.Characters.Components
{
    /// <summary>
    /// 角色移动组件，负责角色的移动、跳跃、重力等核心移动逻辑
    /// Character movement component: handles move, jump, gravity, etc.
    /// </summary>
    public class CharacterMoveComponent : CharacterComponent
    {
        // 迁移自 CharacterBase 的相关字段
        private float _verticalSpeed;
        private bool _isJumping;
        private bool _isFalling;
        private bool _isRolling;
        private bool _startedJumpFromGround;
        private bool _trainJump;
        private float _trainJumpSampleZ;
        private float _glideTimeLeft;
        private float _superJumpOriginal;
        private float _superJumpAltitude;
        private float _jumpHeightNormal;
        private float _gravity;
        private float _gliderGravity;
        private float _glideTime;
        private float _superSneakersJumpApexRatio;
        private bool _jumpUpperCollision;
        private bool _jumpUpperCollisionEvaded;
        private float _jumpUpperCollisionColliderMinY;
        private float _verticalFallSpeedLimit;
        private bool _willSecondJump;
        private bool _inAirJump;
        private float _lastGroundedY;
        private int _trackMovement;
        private int _trackMovementNext;
        private int _ignoredMovement;
        private int _initialTrackIndex;
        private float _characterRotation;
        private int _trackIndexTarget;
        private float _trackIndexPosition;
        private Vector3 _currentDistance;
        private Vector3 _currentPosition;
        private bool _movedSinceLastFixedUpdate;
        private float _x;
        private SuperSneakersJump? _superSneakersJump;
        private AnimationCurve _superSneakersJumpCurve;

        public override void Initialize(CharacterBase character)
        {
            base.Initialize(character);
            // 初始化字段
            _verticalSpeed = character.verticalSpeed;
            _isJumping = character.IsJumping;
            _isFalling = character.IsFalling;
            _isRolling = character.IsRolling;
            _startedJumpFromGround = character._startedJumpFromGround;
            _trainJump = character._trainJump;
            _trainJumpSampleZ = character._trainJumpSampleZ;
            _glideTimeLeft = character._glideTimeLeft;
            _superJumpOriginal = character.SuperJumpAltitude;
            _superJumpAltitude = character.SuperJumpAltitude;
            _jumpHeightNormal = character.jumpHeightNormal;
            _gravity = character.Gravity;
            _gliderGravity = character.gliderGravity;
            _glideTime = character._glideTime;
            _superSneakersJumpApexRatio = character.superSneakersJumpApexRatio;
            _jumpUpperCollision = character._jumpUpperCollision;
            _jumpUpperCollisionEvaded = character._jumpUpperCollisionEvaded;
            _jumpUpperCollisionColliderMinY = character._jumpUpperCollisionColliderMinY;
            _verticalFallSpeedLimit = character.verticalFallSpeedLimit;
            _willSecondJump = character._willSecondJump;
            _inAirJump = character.inAirJump;
            _lastGroundedY = character.LastGroundedY;
            _trackMovement = character._trackMovement;
            _trackMovementNext = character._trackMovementNext;
            _ignoredMovement = character._ignoredMovement;
            _initialTrackIndex = character._initialTrackIndex;
            _characterRotation = character._characterRotation;
            _trackIndexTarget = character._trackIndexTarget;
            _trackIndexPosition = character._trackIndexPosition;
            _currentDistance = character.GamePosition;
            _currentPosition = character.TransformPosition;
            _movedSinceLastFixedUpdate = character._movedSinceLastFixedUpdate;
            _x = character.x;
            _superSneakersJump = character._superSneakersJump;
            _superSneakersJumpCurve = character.superSneakersJumpCurve;
        }

        /// <summary>
        /// 角色前进移动
        /// </summary>
        public void MoveForward()
        {
            // 迁移自 CharacterBase.MoveForward
            Vector3 gamePosition = _currentDistance;
            float nextGamePositionZ = gamePosition.z + _character.CurrentSpeed * TimeMgr.deltaTime;
            Vector3 verticalMove = Vector3.zero;
            bool canMoveUp = true;
            if (_isJumping && _jumpUpperCollision)
            {
                canMoveUp = false;
                if (!_jumpUpperCollisionEvaded && _jumpUpperCollisionColliderMinY < _character.CharacterTrigger.bounds.max.y)
                {
                    if (0f <= _verticalSpeed)
                        verticalMove = -_gravity * TimeMgr.deltaTime * TimeMgr.deltaTime * Vector3.up;
                }
                else
                {
                    verticalMove = Vector3.zero;
                    _jumpUpperCollisionEvaded = true;
                }
            }

            if (_verticalSpeed < 0f || canMoveUp)
                verticalMove = _verticalSpeed * TimeMgr.deltaTime * Vector3.up;

            if (_isJumping && _superSneakersJump.HasValue)
            {
                SuperSneakersJump jump = _superSneakersJump.Value;

                if (_currentDistance.z < jump.EndGamePositionZ)
                {
                    if (canMoveUp)
                    {
                        float y_new = _superSneakersJumpCurve.Evaluate((nextGamePositionZ - jump.StartGamePositionZ) / jump.Length) * _superJumpAltitude + jump.StartGamePositionY;
                        float y_delta = y_new - gamePosition.y;
                        verticalMove = Vector3.up * y_delta;
                    }
                    else
                        verticalMove = Vector3.zero;
                }
                else
                {
                    _superSneakersJump = null;
                    _verticalSpeed = 0f;
                    verticalMove = Vector3.zero;
                }
            }
            Vector3 xzPositionTarget = Route.GetPosition(_x, nextGamePositionZ);
            Vector3 xzPosition = new Vector3(gamePosition.x, 0f, gamePosition.z);
            Vector3 xzMove = xzPositionTarget - xzPosition;

            if (_character.CharacterController.enabled)
            {
                _character.CharacterController.Move(verticalMove + xzMove);
            }
            else
            {
                _character.transform.position = _currentPosition + xzMove;
            }

            // 更新缓存
            _currentDistance = _character.GamePosition;
            _currentPosition = _character.TransformPosition;

            if (_character.CharacterController.isGrounded)
            {
                _lastGroundedY = gamePosition.y;
            }
            _movedSinceLastFixedUpdate = true;
        }

        /// <summary>
        /// 角色跳跃
        /// </summary>
        public bool Jump()
        {
            // 迁移自 CharacterBase.Jump
            bool forgivenJump = !_isJumping && _verticalSpeed <= 0f && _verticalSpeed > CharacterConstant.JUMP_VERTICAL_SPEED;
            bool canJump = _character.CharacterController.isGrounded || forgivenJump;

            if (canJump)
            {
                if (_isJumping && _jumpUpperCollisionEvaded)
                    _jumpUpperCollision = false;

                if (_isRolling)
                {
                    _character.ExpandColliders();
                }
                _glideTimeLeft = 0.0f;
                _character._vTolEnded = false;

                _willSecondJump = true;
                _isJumping = true;
                _isFalling = false;

                _character.IsGrounded.Value = false;

                if (_character.IsJumpingHigher)
                {
                    Vector3 position = _currentDistance;
                    _superJumpAltitude = _superJumpOriginal;

                    SuperSneakersJump jump = new SuperSneakersJump();
                    jump.StartGamePositionZ = position.z;
                    jump.Length = _character.GetJumpDistance(_character.CurrentSpeed, _superJumpAltitude) * _superSneakersJumpApexRatio;
                    jump.EndGamePositionZ = jump.StartGamePositionZ + jump.Length;
                    jump.StartGamePositionY = position.y;

                    _superSneakersJump = jump;
                    _verticalSpeed = 0f;
                }
                else
                {
                    _verticalSpeed = _character.CalculateJumpVerticalSpeed(_character.JumpHeight);
                }

                _character.NotifyOnJump(_character.IsJumpingHigher ? _superJumpAltitude : _jumpHeightNormal, false);
                if (_character.IsRunningOnGround())
                {
                    _startedJumpFromGround = true;
                    _trainJump = false;
                    _trainJumpSampleZ = _currentDistance.z + CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }
            else
            {
                if (_verticalSpeed < 0f)
                {
                    _inAirJump = true;
                }
            }
            return canJump;
        }

        /// <summary>
        /// 应用重力
        /// </summary>
        public void ApplyGravity()
        {
            // 迁移自 CharacterBase.ApplyGravity
            if (_verticalSpeed < 0f && _character.CharacterController.isGrounded)
            {
                if (_startedJumpFromGround && _trainJump && _character.IsRunningOnGround())
                {
                    _character.SendJumpOverTrainEvent();
                }

                if (!_character.IsRunningAir())
                {
                    _startedJumpFromGround = false;
                }

                _character.IsGrounded.Value = true;

                if (_isJumping || _isFalling)
                {
                    _jumpUpperCollision = false;
                    _jumpUpperCollisionEvaded = true;

                    _isJumping = false;
                    _isFalling = false;
                    _character.NotifyOnLanding();
                }

                _verticalSpeed = 0f;
            }
            else
            {
                if (_startedJumpFromGround && _trainJumpSampleZ < _currentDistance.z)
                {
                    RaycastHit hit;
                    if (Physics.Raycast(new Ray(_currentPosition, -Vector3.up), out hit))
                    {
                        if (hit.collider.CompareTag("HitMovingTrain") ||
                            hit.collider.CompareTag("HitTrain"))
                        {
                            _trainJump = true;
                        }
                    }
                    _trainJumpSampleZ += CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }

            if (_isFalling && _character.IsGliderboard)
            {
                if (_glideTimeLeft < _glideTime)
                {
                    _verticalSpeed = _gliderGravity * new AnimationCurve(new Keyframe(0, 0), new Keyframe(1, 1)).Evaluate(_glideTimeLeft / _glideTime);
                    _glideTimeLeft += TimeMgr.deltaTime;
                }
                else
                {
                    _verticalSpeed -= _gliderGravity * TimeMgr.deltaTime;
                }
            }
            else
            {
                _verticalSpeed -= _gravity * TimeMgr.deltaTime;
            }

            if (!_character.CharacterController.isGrounded)
            {
                if (!_isFalling && _verticalSpeed < _verticalFallSpeedLimit && !_isRolling)
                {
                    _isFalling = true;
                    _character.SendHangtimeEvent();
                    _character.IsGrounded.Value = false;
                }
            }
        }

        /// <summary>
        /// 切换轨道
        /// </summary>
        public void ChangeTrackBy(int trackDelta, float duration)
        {
            // TODO: 迁移 CharacterBase.ChangeTrackBy 逻辑
        }

        /// <summary>
        /// 强制切换轨道
        /// </summary>
        public void ForceChangeTrack(int movement, float duration)
        {
            // TODO: 迁移 CharacterBase.ForceChangeTrack 逻辑
        }

        /// <summary>
        /// 初始化轨道索引
        /// </summary>
        public void InitialTrackIndex(int index)
        {
            // TODO: 迁移 CharacterBase.InitialTrackIndex 逻辑
        }

        /// <summary>
        /// 下落移动
        /// </summary>
        public void MoveDrop()
        {
            // TODO: 迁移 CharacterBase.MoveDrop 逻辑
        }

        /// <summary>
        /// 检查空中跳跃
        /// </summary>
        public void CheckInAirJump()
        {
            // TODO: 迁移 CharacterBase.CheckInAirJump 逻辑
        }
    }
} 