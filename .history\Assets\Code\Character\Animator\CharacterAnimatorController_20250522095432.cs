﻿using UnityEngine;
using UnityEngine.Playables;
using System.Collections;
using RG;
using RG.Characters;
using RG.Common;

namespace RG.Common
{
    public class CharacterAnimatorController
    {
        private Animator _animator;
        private CharacterAnimator _characterAnimator;
        private CharacterModel _model;
        private SmoothLayerWeightSetter _layerSetter;

        private bool _isGameEnd = false;
        private bool _isBeAttack = false;
        private Timer _beAttackTimer = null;

        public bool HoverboardActive { get; private set; }
        public bool MagnetActive { get; private set; }
        public bool SteamJumpActive { get; private set; }
        public bool SuperSneakersActive { get; private set; }
        public bool IsRolling { get; private set; }
        public bool JetPackActive { get; private set; }


        public CharacterAnimatorController(CharacterAnimator ca, CharacterModel model)
        {
            if (ca != null)
            {

                this._model = model;
                this._characterAnimator = ca;
                this._animator = ca.Animator;
                this._characterAnimator.OnAnimationEventTriggered += this.OnEventTriggered;

                if (_animator != null && !_layerSetter)
                    _layerSetter = _animator.GetComponent<SmoothLayerWeightSetter>();
            }
        }

        public void ResetDeath()
        {
            this._isGameEnd = false;
        }

        public void OnEventTriggered(CharacterAnimationEventType eventType)
        {
            if (eventType == this._characterAnimator.RollFinishedAnimationEvent)
            {
                HandleRollingEnded();
            }
        }

        public void OnStartPvpKaiChang()
        {
            if (_animator)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_PVPKaiChang);
            }
        }

        public void OnPVPEnd()
        {
            // Turn Around 
            this._isGameEnd = true;
            if (_animator)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_PVPEnd);
            }
        }

        public void OnPVPFinish(bool isCompleted, Sexual sexual)
        {
            this.OnPVPFinish(this.GetFinishClipIndexBySexual(isCompleted, sexual));
        }

        private void OnPVPFinish(int index)
        {
            _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_PVPFinishIndex, index);
            _animator.SetTrigger(CharacterAnimatorConstants.ParamFloat_PVPFinish);
        }

        private int GetFinishClipIndexBySexual(bool isCompleted, Sexual sexual)
        {
            int index = 0;
            int finishStartIndex = 0;
            int unFinishStartIndex = 3;
            int startIndex = isCompleted ? finishStartIndex : unFinishStartIndex;
            switch (sexual)
            {
                case Sexual.Female:
                    index = startIndex + 0;
                    break;
                case Sexual.Male:
                    index = startIndex + 1;
                    break;
                case Sexual.Fat:
                    index = startIndex + 2;
                    break;
            }
            return index;
        }

        public void PVPEnter(bool isMine)
        {
            if (_animator)
            {
                this._characterAnimator.ResetAnimator();

                _animator.SetBool(CharacterAnimatorConstants.ParamFloat_PVPIdleLoop, false);
                int index = SSCRandomContainer.Instance.Random.Range(0, 2);

                _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_RandomPVPIdel, index);

                if (isMine)
                {
                    Timer.ExecuteWithDelay(4.0f, () =>
                    {
                        _animator.SetBool(CharacterAnimatorConstants.ParamFloat_PVPCome_on, true);
                    });
                }
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_PVPKaiChangIdle);
            }
        }

        public IEnumerator WaitForTransitionAndStartRun(float transitionDuration)
        {
            yield return new WaitForSeconds(transitionDuration);

            this.StartRun();
        }

        public void StartRun()
        {
            if (_animator)
            {
                this.ForceRun();
            }
        }

        public void ForceRun()
        {
            if (_animator && !this._isGameEnd && !this._isBeAttack)
            {
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_ForceRun);
            }
        }

        public void SetLowriderState(bool isLowrider)
        {
            _animator.SetBool(CharacterAnimatorConstants.ParamFloat_IsLowrider, isLowrider);
        }

        public void OnJump(bool isSmallJump, bool isDangerous = false)
        {
            if (_animator)
            {
                int triggerId = isDangerous ?
                    CharacterAnimatorConstants.ParamTrigger_DangerousSurface :
                    CharacterAnimatorConstants.ParamTrigger_Jump;
                _animator.SetTrigger(triggerId);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, true);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);
            }
        }

        public void OnRoll()
        {
            if (!_animator) return;

            _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);

            SetGrind(false);

            // If we're already rolling, don't double trigger roll
            if (IsRolling)
                return;

            IsRolling = true;

            _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_Roll);
        }

        public void OnDive()
        {
            if (IsRolling) return;
            IsRolling = true;
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);
            if (_animator)
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_Roll);
        }

        public void OnJetPackStarted()
        {
            this.SetFlightState(true);
        }

        public void OnJetPackFinished()
        {
            this.SetFlightState(false);
        }

        void SetFlightState(bool enabled)
        {
            JetPackActive = enabled;

            if (JetPackActive)
            {
                // Make sure we cancel jump when picking up jetpack
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);

                this._characterAnimator.AnimationConfig.JetPackOverride.ApplyOverride(_animator, this._layerSetter, this._characterAnimator.AnimatorController); // Show hoverboard anims in stead of run anim

                // _animationConfig.JetpackOverride.ApplyOverride(_animator, _defaultAnimatorController);
                // _propsController.ShowFlight(true, flightMode);

                // Jetpack animations are not affected by run speed
                SetRunSpeed(1f);

                // Hide hoverboard when picking jetpack
                if (HoverboardActive)
                    HideHoverboard();
            }
            else
            {
                this._characterAnimator.AnimationConfig.JetPackOverride.RemoveOverride(_animator, this._layerSetter, this._characterAnimator.AnimatorController);

                // _propsController.ShowFlight(false, flightMode);
                // SetRunSpeed(_motor.SpeedModifier);
                // Don't show props if stopping game

                if (!Game.Instance.IsInGame.Value)
                    return;

                // Show hoverboard when leaving jetpack with a board
                // if (HoverboardActive)
                //     ShowHoverboard(CurrentHoverboard, _randomHoverboard != null);
                // else if (SuperSneakersActive)
                //     ShowSuperSneakersAnimation();
            }
        }

        public void OnLaneChanged(OnChangeTrackDirection laneDirection)
        {
            if (!_animator)
                return;

            if (!this._characterAnimator.Grounded)
                return;

            if (laneDirection == OnChangeTrackDirection.Left)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_LaneChangeLeft);
            }
            else if (laneDirection == OnChangeTrackDirection.Right)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_LaneChangeRight);
            }

            SetGrind(false);
        }

        public void HandleRollingEnded()
        {
            IsRolling = false;

            _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);

            _animator.SetBool(CharacterAnimatorConstants.ParamBool_SkipFall, true);
        }

        public void OnLand(float verticalSpeed)
        {
            if (_animator)
            {
                _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_LandSpeed, verticalSpeed);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_SkipFall, false);
            }
        }

        public void OnFallStarted(bool HoverboardActive)
        {
            if (this._isGameEnd)
            {
                return;
            }
            if (_animator)
            {
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, true);
                if (!HoverboardActive)
                    _animator.SetBool(CharacterAnimatorConstants.ParamBool_SkipFall, false);
            }
        }

        public void OnHoldMagnet(bool hasMagnet)
        {
            this._model.MeshCoinMagnet.enabled = hasMagnet;
            MagnetActive = hasMagnet;
            if (_animator)
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_HasMagnet, MagnetActive);
        }

        public void OnSteamJumpStart(bool hasSteamJump)
        {
            SteamJumpActive = hasSteamJump;
        }

        public void OnSteamJumpHangTime()
        {
            if (this.SteamJumpActive)
            {
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, true);
            }
        }

        public void OnActivateHoverboard()
        {
            HoverboardActive = true;
            this.ShowHoverboard();
        }

        public void EnterPvpRankIdle(int index)
        {
            if (this._animator)
            {
                this._animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_PVPRank);
                this._animator.SetFloat(CharacterAnimatorConstants.ParamFloat_PVPRankIndex, (float)index);
            }
        }

        public void EnterPvpLoading()
        {
            if (this._animator)
            {
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_SkipFall, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Falling, false);
                _animator.SetBool(CharacterAnimatorConstants.ParamBool_Jumping, false);

                this._animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_PVPLoadingEnter);
            }
        }

        private void ShowHoverboard()
        {
            this._characterAnimator.AnimationConfig.HoverboardOverride.ApplyOverride(_animator, this._layerSetter, this._characterAnimator.AnimatorController); // Show hoverboard anims in stead of run anim
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_HasHoverboard, HoverboardActive);
            SetRunSpeed(1f);
            _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_GetOnBoard);
        }

        private void HideHoverboard()
        {
            this._characterAnimator.AnimationConfig.HoverboardOverride.RemoveOverride(_animator, this._layerSetter, this._characterAnimator.AnimatorController);
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_HasHoverboard, HoverboardActive);
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_SkipFall, false);
            SetGrind(false);
        }

        public void OnDeactivateHoverboard()
        {
            HoverboardActive = false;
            this.HideHoverboard();
        }

        public void OnBeAttack(BeAttackType type, float stopFrameTime = 0, float duration = 0)
        {
            if (!_animator)
                return;

            this._isBeAttack = true;

            _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_PVPAttackType, (float)type);
            _animator.SetBool(CharacterAnimatorConstants.ParamFloat_ISPVPBeAttack, true);
            _animator.SetTrigger(CharacterAnimatorConstants.ParamFloat_PVPBeAttack);
        }

        public void OnAttackEnd()
        {
            if (_beAttackTimer != null)
            {
                _beAttackTimer.Stop();
                _beAttackTimer = null;
            }
            this._isBeAttack = false;

            if (!_animator) return;
            _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_PVPAttackType, 0);
            _animator.SetBool(CharacterAnimatorConstants.ParamFloat_ISPVPBeAttack, false);
            this.ForceRun();
        }

        public void OnStumble(StumbleType type, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit)
        {
            if (!_animator)
                return;

            SetStumbleParameters(type, horizontalHit, verticalHit);
            _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_Stumble);
        }

        public void OnDieByStumble()
        {
            if (_animator)
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_StumbleDeath);
        }

        public void OnHitByMovingTrain()
        {
            if (_animator)
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_HitMovingTrain);
        }

        public void OnRevive()
        {
            if (this._animator)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_ForceRun);
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_Jump);
            }
        }

        void SetStumbleParameters(StumbleType type, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit)
        {
            _animator.SetInteger(CharacterAnimatorConstants.ParamInt_StumbleType, (int)type);
            _animator.SetInteger(CharacterAnimatorConstants.ParamInt_HorizontalHit, (int)horizontalHit);
            _animator.SetInteger(CharacterAnimatorConstants.ParamInt_VerticalHit, (int)verticalHit);
        }

        void SetGrind(bool showGrind)
        {
            _animator.SetBool(CharacterAnimatorConstants.ParamBool_Grinding, showGrind);
        }

        void SetRunSpeed(float speed)
        {
            if (_animator)
                _animator.SetFloat(CharacterAnimatorConstants.ParamFloat_SpeedMultiplier, speed);
        }

        public void OnRunStart()
        {
            if (_animator)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_ForceRun); // 或者其他合适的触发器
            }
        }

        public void OnRunEnd()
        {
             if (_animator)
            {
                _animator.SetTrigger(CharacterAnimatorConstants.ParamTrigger_Reset); // 改为 ParamTrigger_Reset
            }
        }

    }
}