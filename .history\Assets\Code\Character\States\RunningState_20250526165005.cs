using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class RunningState : CharacterState
    {
        [HideInInspector]
        public float cameraFOV = 40f;

        public bool transitionFromHeight = false;

        private float tunnelStartZ;
        private Curve offsetDeltaCurve = null;

        public const float CHARACTER_CHANGE_TRACK_LENGTH = 30;
        public const float DEFAULT_FOV = 40f;

        public RunPositions currentRunPosition;
        public enum RunPositions { ground, station, train, movingTrain, air };

        private Queue<Collider> GrindedTrains = new Queue<Collider>();
        private int GrindedTrainsBufferSize = 5;

        public event Action RunStarted = null;
        public event Action RunEnded = null;

        private static RunningState instance = null;
        public static RunningState Instance
        {
            get
            {
                return instance ?? (instance = FindObjectOfType(typeof(RunningState)) as RunningState);
            }
        }

        // 状态初始化（进入跑酷时由外部调用）
        public override void InitState()
        {
            // TODO: 事件注册、依赖注入等
        }

        // 状态清理（退出一局时由外部调用）
        public override void ClearState()
        {
            // TODO: 事件注销、引用清理等
        }

        private void _updateSpeed()
        {
            Character.SetCurrentSpeed(_game.Speed);
        }

        public override void Enter()
        {
            RunStarted?.Invoke();
            Character.charSpeed.Begin();
            transitionFromHeight = false;

            if (Character.GamePosition.y > 70f && Character.GamePosition.y < 125f)
            {
                transitionFromHeight = true;
            }

            Character.CharacterTrigger.enabled = true;
            Character.LastGroundedY = Character.GamePosition.y;
        }

        public override void Exit()
        {
            RunEnded?.Invoke();
        }

        public override void OnStateUpdate()
        {
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
            Character.runningTime += TimeMgr.deltaTime;
            _updateSpeed();

            if (Character.isDropMoving)
            {
                Character.MoveDrop();
            }
            else if (!Character.isStopRunning)
            {
                Character.ApplyGravity();
                Character.MoveForward();
            }

            if (transitionFromHeight)
            {
                if (Character.IsGrounded.Value || Character.inAirJump && Character.CharacterController.isGrounded)
                {
                    transitionFromHeight = false;
                    Character.ForceLeaveSubway();
                }
            }

            Character.CheckInAirJump();
            UpdateInAirRunPosition();

            if (Character.GamePosition.z > PvpMgr.ROUTE_LENGTH)
            {
                Character.charSpeed.ChangeState(CharacterSpeedState.Idle);
                NetDirector.Rule_PostZ(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position);
                NetDirector.PostRoom_EndGame(10, PhotonNetwork.player.ID, RG.XPluginAccount._userid, true);
                Game.Instance.ChangeState(GameStateType.Die);
                NetDirector.Post_GetRankIdx(XPluginAccount._userid, Game.Instance.Character.gameObject.transform.position.z, RG.XPluginAccount._userid);
            }
            else if (PvpMgr.Inst.IsEnterEndGame)
            {
                if (PvpMgr.Inst.EnterEndGameTime - PhotonNetwork.time < 0)
                {
                    Character.charSpeed.ChangeState(CharacterSpeedState.Idle);
                    Game.Instance.ChangeState(GameStateType.End);
                }
            }
        }

        private void UpdateInAirRunPosition()
        {
            if (!Character.CharacterController.isGrounded)
                currentRunPosition = RunPositions.air;
        }

        private void LandedOnTrain(Collider trainCollider)
        {
            if (Character.BoardState.enabled)
            {
                if (!GrindedTrains.Contains(trainCollider))
                {
                    if (GrindedTrains.Count > GrindedTrainsBufferSize)
                        GrindedTrains.Dequeue();
                    GrindedTrains.Enqueue(trainCollider);
                }
            }
        }

        private void UpdateGroundTag(CharacterBase character)
        {
            var ray = new Ray(character.CharacterRoot.position, -Vector3.up);
            RaycastHit hitInfo;
            if (Physics.Raycast(ray, out hitInfo))
            {
                switch (hitInfo.collider.tag)
                {
                    case "Ground":
                        currentRunPosition = RunPositions.ground;
                        break;
                    case "HitTrain":
                        currentRunPosition = RunPositions.train;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "HitMovingTrain":
                        currentRunPosition = RunPositions.movingTrain;
                        LandedOnTrain(hitInfo.collider);
                        break;
                    case "Station":
                        currentRunPosition = RunPositions.station;
                        break;
                    default:
                        break;
                }
            }
        }

        public override void HandleCriticalHit()
        {
            Character.CharacterCamera.Shake();
            if (Character.IsStumbling)
            {
                Character.StopStumble();
            }
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            Debug.Assert(Character != null, "[RunningState] Character 为空，状态未正确初始化！");
            if (Character == null)
            {
                Debug.LogError("[RunningState] Character is null! 可能状态未初始化或被错误 new 出来。");
                return;
            }
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
            if (Character.isStopRunning) return;
            if (PowerupMgr.Inst.IsInDevil)
            {
                switch (swipeDir)
                {
                    case SwipeDir.Up:
                        swipeDir = SwipeDir.Down;
                        break;
                    case SwipeDir.Down:
                        swipeDir = SwipeDir.Up;
                        break;
                    case SwipeDir.Left:
                        swipeDir = SwipeDir.Right;
                        break;
                    case SwipeDir.Right:
                        swipeDir = SwipeDir.Left;
                        break;
                    default:
                        break;
                }
            }

            float currentSpeed = Character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }
            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(-1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(-1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Right:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(+1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(+1, CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Up:
                    Jump();
                    break;
                case SwipeDir.Down:
                    Character.Roll();
                    if (RouteManager.Instance.IsCharacterInAnyRoute)
                    {
                        Vector3 routeRelativeCharacterPosition = RouteManager.Instance.CharacterRoute.GetRelativePosition(Character.GamePosition);
                        if (routeRelativeCharacterPosition.y > 200)
                        {
                            RGLogger.LogWarning("Character", "Accellerated Fall");
                            Character.verticalSpeed = -Character.CalculateJumpVerticalSpeed(routeRelativeCharacterPosition.y);
                        }
                    }
                    break;
            }
        }

        private void Jump()
        {
            Character.Jump();
        }

        public override bool CanTransitionTo(CharacterState nextState)
        {
            if (nextState == null)
                return false;

            // 检查是否可以切换到目标状态
            if (nextState is JumpingState)
            {
                return !Character.IsJumping && Character.IsGrounded.Value;
            }
            else if (nextState is RollingState)
            {
                return !Character.IsRolling && Character.IsGrounded.Value;
            }
            else if (nextState is StumblingState)
            {
                return !Character.IsStumbling;
            }
            else if (nextState is DeathState)
            {
                return true;
            }

            return false;
        }
    }
}
