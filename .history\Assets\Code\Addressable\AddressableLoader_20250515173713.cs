using System.Threading.Tasks;
using RG.Common.Logger;

namespace RG
{
    public sealed class AddressableLoader
    {
        private string _id = string.Empty;
        
        private object _asset = null;

        public AddressableLoader(string assetId) 
        {
            _id = assetId;
            _asset = null;
        }

        public async Task Load<T>(System.Action<bool, T> action = null) where T : UnityEngine.Object
        {
            if (null != _asset)
            {
                if (null != action)
                {
                    action.Invoke(true, GetAsset<T>());
                }
                return;
            }
            try
            {
                T localAsset = AddressableLocalMgr.Inst.GetAsset<T>(_id);
                if (null != localAsset)
                {
                    _asset = localAsset;
                    if (null != action)
                    {
                        action.Invoke(true, GetAsset<T>());
                    }
                    return;
                }

                // 本地资源不存在，尝试从 Addressables 加载
                UnityEngine.AddressableAssets.AssetReference asset = new UnityEngine.AddressableAssets.AssetReference(_id);
                UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationHandle<T> handle = asset.LoadAssetAsync<T>();
                await handle.Task;
                bool result = handle.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded;
                if (result)
                {
                    _asset = handle.Result;
                }
                else
                {
                    RGLogger.LogError("Resource", $"AssetId: {_id}, 类型: {typeof(T).Name}, 状态: {handle.Status}");
                }
                if (null != action)
                {
                    action.Invoke(result, GetAsset<T>());
                }
            }
            catch (System.Exception e)
            {
                RGLogger.LogException("Resource", e);
                if (null != action)
                {
                    action.Invoke(false, null);
                }
            }
        }

        public void Unload()
        {
            _asset = null;
        }

        public T GetAsset<T>() where T : class
        {
            return null == _asset ? null : _asset is T ? _asset as T : null;
        }
    }
}