using System.Collections.Generic;
using UnityEngine;
using RG.Level;

namespace RG.Routes
{
    public struct RouteConstraint
    {
        public readonly EnvironmentKind[] AllowedEnvironmentKinds;

        public RouteConstraint(EnvironmentKind[] allowedEnvironmentKinds)
        {
            AllowedEnvironmentKinds = allowedEnvironmentKinds;
        }

        public static RouteConstraint None = new RouteConstraint(new EnvironmentKind[0]);
    }
}
