# CharacterBase 重构变更记录

## 2024-03-21 初始重构计划

### 1. 事件系统重构
- 计划创建事件基类 `CharacterEvent`
- 计划创建事件管理器 `CharacterEventManager`
- 计划将现有事件迁移到新系统

### 2. 状态系统重构
- 计划创建状态基类 `CharacterState`
- 计划创建状态管理器 `CharacterStateManager`
- 计划将现有状态迁移到新系统

### 3. 组件系统重构
- 计划创建组件基类 `CharacterComponent`
- 计划创建组件管理器 `CharacterComponentManager`
- 计划将现有组件迁移到新系统

### 4. 配置系统重构
- 计划创建配置基类 `CharacterConfig`
- 计划创建配置管理器 `CharacterConfigManager`
- 计划将现有配置迁移到新系统

## 变更记录

### 2024-03-21 状态系统重构 - 第一阶段
1. 创建了状态基类 `CharacterState`
   - 添加了基本的生命周期方法：Enter、Exit、OnStateUpdate、OnStateFixedUpdate
   - 添加了状态转换检查方法 CanTransitionTo
   - 添加了初始化方法 Initialize

2. 创建了状态管理器 `CharacterStateManager`
   - 实现了状态的注册功能 RegisterState
   - 实现了状态切换功能 ChangeState
   - 实现了状态的更新功能 Update 和 FixedUpdate

3. 方法名修改
   - 将 `Update` 改为 `OnStateUpdate`，避免与 Unity 生命周期函数冲突
   - 将 `FixedUpdate` 改为 `OnStateFixedUpdate`，避免与 Unity 生命周期函数冲突
   - 更新了状态管理器中的相应方法调用

4. 命名空间修复
   - 添加了 `RG.Characters` 命名空间引用
   - 修复了 `CharacterBase`、`Character`、`CharacterRendering` 等类型的引用问题

5. 下一步计划：
   - 创建具体的状态类（如 RunningState、JumpingState 等）
   - 在 CharacterBase 中集成状态管理器
   - 将现有的状态逻辑迁移到新的状态系统中

### 2024-03-21 状态系统重构 - 第二阶段
1. 重构了 `RunningState` 类
   - 将原有的协程式状态更新改为基于 Update 的状态更新
   - 实现了 Enter、Exit、OnStateUpdate 等生命周期方法
   - 保留了原有的功能，包括：
     - 跑步速度更新
     - 地面检测
     - 轨道切换
     - 跳跃和翻滚
     - 碰撞检测
     - 游戏结束条件检查

2. 待解决的问题：
   - 需要修复 `CharacterBase` 中缺少的方法：
     - ForceLeaveSubway
     - IsStumbling
     - StopStumble
   - 需要修复 `Character` 的 Instance 属性访问问题

3. 下一步计划：
   - 修复 `CharacterBase` 中缺少的方法
   - 完善 `RunningState` 的状态转换逻辑
   - 添加状态转换的条件检查
   - 实现其他状态类（如 JumpingState、RollingState 等）

### 2024-03-21 状态系统重构 - 第三阶段
1. 在 `CharacterBase` 类中添加了缺少的方法：
   - 添加了 `ForceLeaveSubway` 方法，用于强制角色离开地铁
   - 添加了 `IsStumbling` 属性，用于检查角色是否处于绊倒状态
   - 添加了 `StopStumble` 方法，用于停止角色的绊倒状态

2. 下一步计划：
   - 完善 `RunningState` 的状态转换逻辑
   - 添加状态转换的条件检查
   - 实现其他状态类（如 JumpingState、RollingState 等）
   - 在 `CharacterBase` 中集成状态管理器

### 2024-03-21 状态系统重构 - 第四阶段
1. 完善了 `RunningState` 的状态转换逻辑：
   - 实现了 `CanTransitionTo` 方法，用于检查是否可以切换到目标状态
   - 添加了以下状态转换条件：
     - 跳跃状态：角色不处于跳跃状态且在地面上
     - 翻滚状态：角色不处于翻滚状态且在地面上
     - 绊倒状态：角色不处于绊倒状态
     - 死亡状态：可以随时切换

2. 待解决的问题：
   - 需要创建其他状态类：
     - JumpingState
     - RollingState
     - StumblingState
     - DeathState
   - 需要修复 `Character` 的 Instance 属性访问问题

3. 下一步计划：
   - 创建其他状态类
   - 修复 `Character` 的 Instance 属性访问问题
   - 在 `CharacterBase` 中集成状态管理器
   - 实现状态之间的转换逻辑

### 2024-03-21 状态系统重构 - 第五阶段
1. 创建了 `JumpingState` 类：
   - 实现了跳跃状态的基本功能：
     - 普通跳跃和超级跳跃
     - 二段跳支持
     - 重力应用
     - 落地检测
   - 添加了状态转换条件：
     - 可以切换到跑步状态（当角色落地时）
     - 可以切换到死亡状态（随时）
   - 实现了滑动手势处理：
     - 左右滑动切换轨道
     - 下滑触发翻滚

2. 待解决的问题：
   - 需要创建其他状态类：
     - RollingState
     - StumblingState
     - DeathState
   - 需要修复 `Character` 的 Instance 属性访问问题
   - 需要在 `CharacterBase` 中添加 `CharacterStateManager` 属性

3. 下一步计划：
   - 创建其他状态类
   - 修复 `Character` 的 Instance 属性访问问题
   - 在 `CharacterBase` 中集成状态管理器
   - 实现状态之间的转换逻辑

### 2024-03-21 状态系统重构 - 第六阶段
1. 修复了 `Character` 的 Instance 属性访问问题：
   - 在 `CharacterBase` 类中添加了静态 `Instance` 属性
   - 实现了单例模式的获取逻辑

2. 创建了 `StumblingState` 类：
   - 实现了绊倒状态的基本功能：
     - 绊倒动画和效果
     - 绊倒持续时间控制
     - 重力应用
     - 落地检测
   - 添加了状态转换条件：
     - 可以切换到跑步状态（当绊倒结束时）
     - 可以切换到死亡状态（随时）
   - 实现了绊倒信息的设置方法

3. 创建了 `DeathState` 类：
   - 实现了死亡状态的基本功能：
     - 设置死亡标志
     - 处理死亡事件
   - 添加了状态转换条件：
     - 死亡状态是终态，不能切换到其他状态
   - 禁用了所有滑动手势处理

4. 下一步计划：
   - 在 `CharacterBase` 中集成状态管理器
   - 实现状态之间的转换逻辑
   - 开始事件系统的重构

### 2024-06-XX 组件系统重构 - 第一阶段

1. **创建组件基类 `CharacterComponent`**
   - 所有角色功能组件继承，支持 `Initialize(CharacterBase)` 注入宿主。
   - 组件内通过 `_character` 访问宿主对象，解耦主逻辑。

2. **移动相关功能组件化**
   - 新建 `CharacterMoveComponent`，迁移以下方法和相关字段：
     - `MoveForward`、`Jump`、`ApplyGravity`、`ChangeTrackBy`、`ForceChangeTrack`、`InitialTrackIndex`、`MoveDrop`、`CheckInAirJump`
   - 组件内实现与原 `CharacterBase` 完全一致的逻辑，所有依赖字段一并迁移为私有成员。
   - 组件初始化时从宿主同步初始状态，后续状态变更由组件自身维护。

3. **兼容性转发与外部调用**
   - `CharacterBase` 保留上述方法的同名接口，内部全部通过 `GetComponent<CharacterMoveComponent>()` 转发（加 TODO 标记，后续可彻底移除）。
   - 保证所有外部调用和子类重写不受影响，支持平滑过渡和回滚。

4. **访问修饰符一致性修复**
   - 将 `IsRunningOnGround`、`IsRunningAir` 及其所有 override 方法的访问修饰符统一为 `public`，解决 Unity C# 继承访问级别冲突。

5. **子类特殊逻辑兼容**
   - `Character` 子类的 `ChangeTrackBy`、`Jump` 等 override 方法，依然可在调用前后插入自定义逻辑（如 PvpMgr 逻辑、动画、特效等），再调用 `base` 实现，兼容原有差异化需求。

6. **遗留与后续建议**
   - 组件管理器（如自动挂载、生命周期统一调度）尚未落地，建议后续补充。
   - 其它功能（如能力、道具、AI等）可按同样模式组件化。
   - 所有兼容性转发均加 TODO，后续可逐步替换为组件直接调用。
   - 建议补充单元测试和极端场景测试，确保无回归。

### 2024-06-XX Bug修复 - 重构过程中发现的问题

#### 1. **严重Bug：状态管理器循环调用** ✅ 已修复
**问题**: `CharacterStateManager.ChangeState<T>()` 调用 `_character.ChangeState<T>()`，而 `_character.ChangeState<T>()` 又会调用 `_stateManager.ChangeState<T>()`，形成无限递归。

**修复方案**:
- 将状态切换逻辑完全移到 `CharacterStateManager` 中
- `CharacterBase` 的状态相关方法改为通过 `_stateManager` 访问
- 移除 `CharacterBase` 中重复的状态管理代码

#### 2. **数据同步问题** ✅ 已修复
**问题**: `CharacterMoveComponent` 在初始化时从 `CharacterBase` 复制数据，但运行时两者数据不同步，可能导致状态不一致。

**修复方案**:
- 在 `CharacterMoveComponent` 中添加 `SyncToCharacterBase()` 方法
- 在每个修改状态的方法（`MoveForward`, `Jump`, `ApplyGravity`）后调用同步方法
- 添加 TODO 注释，建议后续使用属性访问器而不是数据复制

#### 3. **状态转换逻辑优化** ✅ 已修复
**问题**: `RunningState` 的状态转换条件不够合理，例如从跑步状态切换到跳跃状态的条件。

**修复方案**:
- 修正跳跃状态转换条件：从"不在跳跃且在地面"改为"在地面且可以跳跃"
- 优化翻滚状态转换：允许在地面或空中翻滚
- 添加详细注释说明状态转换逻辑

#### 4. **代码清理** ✅ 已完成
**问题**: `CharacterBase` 中存在未使用的重复代码和字段。

**修复方案**:
- 移除未使用的 `RegisterState` 方法
- 移除重复的 `_stateMap` 和 `_currentState` 字段
- 简化状态更新逻辑，统一通过 `CharacterStateManager` 处理

### 2024-06-XX PVP战斗初始化问题修复 ✅ 已修复

#### 问题描述
用户报告在大厅无法进入战斗，错误信息显示：
```
[Pvp] 触发时间在:0秒后  帧数:3.505221E+10 当前帧:1564
```

#### 根本原因分析
1. **状态管理器初始化时机问题**: 状态管理器在 `Awake` 中初始化，但 `Game.Instance.Character.GetState<RunningState>()` 在 `Game.Awake` 中被调用，此时可能状态管理器还未完全初始化
2. **异常帧数值**: 帧数显示为 `3.505221E+10`，表明某处的计算出现了数值溢出
3. **状态获取缺乏安全检查**: 直接调用 `GetState<RunningState>()` 没有空值检查

#### 修复方案
1. **调整初始化顺序**: 将状态管理器初始化从 `Awake` 移到 `Initialize` 方法中，确保所有依赖都已准备好
2. **添加安全检查**: 在 `Game.cs` 中添加 `GetRunningStateSafely()` 方法，包含异常处理和空值检查
3. **延迟状态设置**: 如果初始化时无法获取状态，在 `StartGame` 时重新尝试获取

#### 修改的文件
- `CharacterBase.cs`: 调整状态管理器初始化时机
- `Game.cs`: 添加安全的状态获取方法和错误处理
- `PvpDirector.Logic.cs`: 添加PVP启动前的角色初始化检查

#### 进一步修复 (第二轮)
用户反馈仍然出现 "无法获取 Running 状态" 错误，进行了以下增强修复：

1. **PVP启动流程优化**: 在 `PvpDirector.OnClickPvp` 中添加 `EnsureCharacterInitializedAndStartGame()` 方法
2. **详细的初始化检查**: 确保 Character.Instance、IsInitialized 状态、CharacterStateManager 都正确初始化
3. **增强的错误处理**: 添加更详细的日志记录，便于问题定位
4. **强制重新初始化**: 在 `Game.StartGame` 中添加强制重新初始化逻辑

### 2024-06-XX Unity编辑器序列化问题修复 ✅ 已修复

#### 问题描述
用户报告Unity编辑器出现序列化错误：
```
UnityEditor.Editor+SerializedObjectNotCreatableException: Object at index 0 is null
```

#### 根本原因分析
1. **状态类继承问题**: `CharacterState` 原本继承自 `PvpLogicBehaviour`（MonoBehaviour），但状态对象现在通过 `new T()` 创建，不是Unity GameObject组件
2. **序列化属性冲突**: `CharacterState` 中有 `[SerializeField]` 字段，`RunningState` 中有 `[HideInInspector]` 属性，这些Unity特有属性在非MonoBehaviour类中会导致序列化问题

#### 修复方案
1. **移除MonoBehaviour继承**: 将 `CharacterState` 改为普通类，不再继承 `PvpLogicBehaviour`
2. **移除Unity特有属性**: 移除 `[SerializeField]`、`[HideInInspector]` 等Unity特有属性
3. **保持功能完整性**: 确保状态系统功能不受影响

#### 修改的文件
- `CharacterState.cs`: 移除 `PvpLogicBehaviour` 继承和 `[SerializeField]` 属性
- `RunningState.cs`: 移除 `[HideInInspector]` 属性

### 2024-06-XX 状态系统单例遗留引用彻底移除

- 修复 Character.cs 第179行等历史遗留的 RunningState.Instance 引用，全部替换为 GetState<RunningState>()。
- 明确禁止任何角色状态类使用单例或 new，全部由 StateManager 统一管理。
- 兼容性处理：如有外部依赖单例的逻辑，全部加 TODO 标记，后续彻底移除。

### 2024-06-XX 状态系统单例移除与注入修复

1. **移除 RunningState.Instance 单例写法**
   - 状态对象全部由 CharacterStateManager 统一创建和管理，禁止单例和 new。
   - 删除 RunningState.Instance 相关代码，防止野生状态对象未初始化导致的 null 问题。

2. **全局替换 RunningState.Instance/new RunningState()**
   - 所有引用全部替换为 CharacterBase.GetState<RunningState>()，并加 TODO 标记。
   - 兼容性处理：如有外部依赖单例的逻辑，全部加 TODO 标记，后续彻底移除。

3. **CharacterBase 新增 GetState<T>() 方法**
   - 便于外部安全获取状态对象，防止直接 new。

4. **遗留与建议**
   - 建议彻底排查其它状态类是否有类似单例写法，全部移除。
   - 后续所有状态对象必须通过 StateManager 注册和注入，禁止 new。

### 2024-06-XX 角色模型空引用防御与日志增强

- GetModelRotation/SetModelRotation/ResetModelRotation 增加 _characterModel null 检查和详细日志，防止空引用崩溃。
- Character.InitModel() 获取不到 CharacterModel 时输出警告日志，便于资源和初始化流程排查。
- 建议后续补充自动化测试，确保角色模型初始化流程健壮。

### 2024-06-XX 角色模型引用一致性修复

- CharacterRendering 动态实例化模型后，主动将 _model 赋值给 _base.CharacterModel，确保主逻辑引用不为 null。
- Character.InitModel() 不再重复查找和初始化 CharacterModel，只保留引用赋值和警告日志，避免和动态实例化冲突。
- 彻底解决重构后 _characterModel 为 null 的所有潜在问题。

### 2024-06-XX 角色相机 FOV 设置防御与初始化优化

- CharacterCamera.UpdateFOV 增加多重 null 检查和详细日志，防止 Game.Instance/Character/状态为 null 时崩溃。
- 建议后续优化初始化流程，避免环依赖导致的早期空引用。

（后续的变更将按时间顺序记录在这里）