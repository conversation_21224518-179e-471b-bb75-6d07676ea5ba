using UnityEngine;
using UnityEditor;
using RG.Characters.Config;
using System.Collections.Generic;

namespace RG.Characters.Config.Editor
{
    public class CreateDefaultConfigs : EditorWindow
    {
        [MenuItem("Game/Character/Create Default Configs")]
        public static void CreateConfigs()
        {
            // 创建配置目录
            string configPath = "Assets/Resources/Config/Characters";
            if (!AssetDatabase.IsValidFolder(configPath))
            {
                AssetDatabase.CreateFolder("Assets/Resources/Config", "Characters");
            }
            
            // 创建默认角色配置
            CreateCharacterConfigs(configPath);
            
            // 保存资源
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("默认配置已创建完成！");
        }
        
        private static void CreateCharacterConfigs(string configPath)
        {
            // 创建默认角色配置
            var defaultCharacter = CreateCharacterConfig(configPath, "default", "默认角色");
            SetDefaultCharacterConfig(defaultCharacter);
            
            // 创建快速角色配置
            var fastCharacter = CreateCharacterConfig(configPath, "fast", "快速角色");
            SetFastCharacterConfig(fastCharacter);
            
            // 创建跳跃角色配置
            var jumpCharacter = CreateCharacterConfig(configPath, "jump", "跳跃角色");
            SetJumpCharacterConfig(jumpCharacter);
        }
        
        private static CharacterConfig CreateCharacterConfig(string configPath, string characterId, string characterName)
        {
            // 创建基础配置
            var characterConfig = ScriptableObject.CreateInstance<CharacterConfig>();
            characterConfig.CharacterId = characterId;
            characterConfig.CharacterName = characterName;
            AssetDatabase.CreateAsset(characterConfig, $"{configPath}/{characterId}_CharacterConfig.asset");
            
            // 创建状态配置
            var stateConfig = ScriptableObject.CreateInstance<CharacterStateConfig>();
            stateConfig.CharacterId = characterId;
            AssetDatabase.CreateAsset(stateConfig, $"{configPath}/{characterId}_StateConfig.asset");
            
            return characterConfig;
        }
        
        private static void SetDefaultCharacterConfig(CharacterConfig config)
        {
            // 基础属性
            config.BaseSpeed = 30f;
            config.Gravity = 200f;
            
            // 跳跃相关
            config.JumpHeight = 20f;
            config.SuperJumpHeight = 40f;
            config.SuperJumpApexRatio = 0.5f;
            
            // 滑行相关
            config.GlideTime = 0.1f;
            config.GliderGravity = 0.01f;
            
            // 碰撞相关
            config.CharacterControllerHeight = 4f;
            config.CharacterControllerCenter = new Vector3(0, 2f, 0);
            config.CharacterTriggerHeight = 4f;
            config.CharacterTriggerCenter = new Vector3(0, 4f, 0);
            
            // 轨道相关
            config.InitialTrackIndex = 1;
            config.TrackChangeDuration = 0.3f;
            
            // 网络同步
            config.PositionSyncFrequency = 0.1f;
            config.StateSyncFrequency = 0.2f;
            
            // 性能优化
            config.EnableObjectPool = true;
            config.ObjectPoolInitialSize = 10;
            config.ObjectPoolMaxSize = 50;
        }
        
        private static void SetFastCharacterConfig(CharacterConfig config)
        {
            // 继承默认配置
            SetDefaultCharacterConfig(config);
            
            // 修改速度相关属性
            config.BaseSpeed = 40f;
            config.TrackChangeDuration = 0.2f;
        }
        
        private static void SetJumpCharacterConfig(CharacterConfig config)
        {
            // 继承默认配置
            SetDefaultCharacterConfig(config);
            
            // 修改跳跃相关属性
            config.JumpHeight = 30f;
            config.SuperJumpHeight = 60f;
            config.Gravity = 150f;
        }
    }
} 