using UnityEngine;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using RG.Common;
using RG.Cameras;
using RG.Characters.States;
using RG.Routes;
using System;
using RG.Level;
using RG.Common.Logger;
using RG;

namespace RG.Characters
{
    public class CharacterRendering : MonoBehaviour
    {
        private static CharacterRendering _instance = null;
        public static CharacterRendering Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType(typeof(CharacterRendering)) as CharacterRendering;
                }
                return _instance;
            }
        }

        private Coroutine _startRoutine;
        [SerializeField]
        private CharacterAnimator _characterAnimator;
        [SerializeField]
        private GameObject teleboardWarpPrefab;
        [SerializeField]
        private Material teleboardFXMaterial;
        [SerializeField]
        private GameObject characterModelPrefab;


        public Animations animations = new Animations();
        public CharacterModel CharacterModel { get { return _model; } }
        public CharacterBase CharacterBase => this._base;
        public CharacterAnimator CharacterAnimator => this._characterAnimator;

        private Game _game;
        private CharacterBase _base;
        private CharacterModel _model;
        private CharacterCamera _camera;
        private CharacterController _characterController;
        private MeshRenderer _shadowMR;
        private SteamJumpState _steamJumpState;
        private GameObject _h_goCached;
        private BoardAbility[] _h_Ability;
        private GameObject _t_Go;
        private Renderer _t_Render = null;
        private AnimationState _t_Animation;
        private bool _t_isFxActive = false;
        private Transform _t_ExtraFx = null;
        private ParticleSystem[] _allParticle;
        private Material[] _headMaterialsCached;
        private Material _modelMaterialCached;
        private Material _customMaterialCached;
        private Material _currentHoverboardMaterial;
        private Material[] _currentHoverboardCustomMaterials;
        private Dictionary<BoardAbility, Transform> _h_ExtraEffDic = new Dictionary<BoardAbility, Transform>();
        private Dictionary<BoardAbility, Transform> _h_AbilityEffDic = new Dictionary<BoardAbility, Transform>();
        private bool _isCachedCustomizationState = true;
        private bool _isRolling = false;
        private RouteManager _routeManager;

        private bool _isInitialized = false;
        public bool IsInitialized => _isInitialized;

        private void InitValue()
        {
            _game = Game.Instance;
            _base = Character.Instance;
            _characterController = Character.Instance.CharacterController;
            _steamJumpState = SteamJumpState.Instance;
            _camera = CharacterCamera.Instance;
            _routeManager = RouteManager.Instance;
        }

        private void RegisterGameEvent()
        {
            _routeManager.OnRouteCreated += RouteManager_OnRouteCreated;
            Character.Instance.OnChangeTrack += OnChangeTrack;
            Character.Instance.OnStumble += OnStumble;
            Character.Instance.OnRevive += OnRevive;
            Character.Instance.OnResetTeleportFX += ResetCharacterTeleportFX;
            Character.Instance.OnHitByTrain += OnHitByTrain;
            Character.Instance.OnJump += OnJump;
            Character.Instance.OnJumpIfHitByTrain += OnJump;
            Character.Instance.OnRoll += OnRoll;
            Character.Instance.OnHangtime += Character_OnHangtime;
            Character.Instance.IsGrounded.OnChange += OnChangeIsGrounded;
            Character.Instance.OnTeleboardStart += HandleOnTeleboardStart;
            Character.Instance.OnRouteDone += OnRouteDone;
            Character.Instance.OnLanding += OnLanding;

            Character.Instance.BoardState.OnJump += OnJump;
            Character.Instance.BoardState.OnRun += OnRun;
            Character.Instance.BoardState.OnEndHoverboard += OnSwitchToRunning;

            Character.Instance.OnJetPack += OnJetPack;

            InitializeCharacterRenderingEffects();

                Game.OnMenu += OnStageMenuSequence;
                Game.OnIntro += OnIntroRun;
                _game.IsInGame.OnChange += IsInGame_OnChange;
                _steamJumpState.OnStart += SteamJumpOnStart;
                _steamJumpState.OnHangTime += SteamJumpOnHangTime;
                _steamJumpState.OnStop += SteamJumpOnStop;
            }

        public void Initialize()
        {
            if (_isInitialized) return;
            
            InitValue();
            InitModel();
            
            if (CharacterModel != null)
            {
                Character.Instance.SetCharacterModel(CharacterModel);
            }
            else
            {
                Debug.LogError("[CharacterRendering] Initialize: CharacterModel 创建失败");
            }
            
            RegisterGameEvent();
            
            _isInitialized = true;
        }

        protected void Awake()
        {

        }

        protected void OnDestroy()
        {
            if (_routeManager != null)
            {
                _routeManager.OnRouteCreated -= RouteManager_OnRouteCreated;
            }

            if (_startRoutine != null)
            {
                StopCoroutine(_startRoutine);
                _startRoutine = null;
            }
            _model.OnUseHoverboard -= this._onUseHoverboard;
            _model.OnEndHoverboard -= this._onEndHoverboard;
            this._characterAnimator = null;
        }

        private void InitModel()
        {
            GameObject go = Instantiate(characterModelPrefab) as GameObject;
            go.transform.parent = transform;
            go.transform.localPosition = Vector3.zero;

            _allParticle = go.GetComponentsInChildren<ParticleSystem>();

            _model = go.GetComponent<CharacterModel>();
            _shadowMR = _model.MeshBlobShadow;

            if (_base != null)
            {
                _base.CharacterModel = _model;
            }

            if (this._characterAnimator == null)
            {
                this._characterAnimator = _model.Animator;
                this._characterAnimator.SetCharacterRendering(this);
            }

            _model.OnUseHoverboard -= this._onUseHoverboard;
            _model.OnUseHoverboard += this._onUseHoverboard;
            _model.OnEndHoverboard -= this._onEndHoverboard;
            _model.OnEndHoverboard += this._onEndHoverboard;
        }

        private void InitializeCharacterRenderingEffects()
        {
            if (teleboardWarpPrefab == null)
            {
                Debug.LogError("[CharacterRendering] teleboardWarpPrefab 未赋值！");
                return;
            }
            if (_base == null)
            {
                Debug.LogError("[CharacterRendering] _base 未初始化！");
                return;
            }
            if (_base.transform == null)
            {
                Debug.LogError("[CharacterRendering] _base.transform 未初始化！");
                return;
            }
            _t_Go = Instantiate(teleboardWarpPrefab) as GameObject;
            _t_Go.transform.parent = _base.transform;
            _t_Go.SetActive(false);
        }

        public void OnHoldMagnet()
        {
            this.CharacterAnimator.Controller.OnHoldMagnet(true);
        }

        public void OnHideMagnet()
        {
            this.CharacterAnimator.Controller.OnHoldMagnet(false);
        }

        private void _onUseHoverboard()
        {
            this.CharacterAnimator.Controller.OnActivateHoverboard();
        }

        private void _onEndHoverboard()
        {
            this.CharacterAnimator.Controller.OnDeactivateHoverboard();
        }

        #region Events
        public void OnIntroRun()
        {
            foreach (ParticleSystem particle in _allParticle)
            {
                particle.Stop();
            }

            OnSwitchToRunning();

            if (_startRoutine != null)
            {
                StopCoroutine(_startRoutine);
            }

            _model.StopEyeAnimations();
            this.CharacterAnimator.Controller.ResetDeath();
        }

        public void StopIntroRun()
        {
            if (_startRoutine != null)
            {
                StopCoroutine(_startRoutine);
            }
        }

        public void OnRun()
        {
            if (!_base.IsFalling && !_base.IsJumping && !_base.IsRolling && !_base.IsDeath)
            {
                string runAnimation = animations.Run;
                if (_characterController.isGrounded)
                {
                    this.CharacterAnimator.Controller.ForceRun();
                }
            }
        }

        private void OnChangeTrack(OnChangeTrackDirection direction)
        {
            if ((_base.IsGrounded.Value || _base is Robot) && !SteamJumpState.Instance.isActive && !_base.IsDeath)
            {
                string dodgeAnimation = direction == OnChangeTrackDirection.Left ? animations.DodgeLeft : animations.DodgeRight;
                this.CharacterAnimator.Controller.OnLaneChanged(direction);
            }
        }

        public void OnStumble(string strAniType, StumbleType stumbleType, StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, string colliderName, bool safeStumble)
        {
            StumbleAniType aniType = (StumbleAniType)Enum.Parse(typeof(StumbleAniType), strAniType);
            if (_base is Character)
            {
                LogicalOperationType logicalOp = (LogicalOperationType)Enum.Parse(typeof(LogicalOperationType), strAniType);
                PvpMgr.Inst.SendLogicalOperation(logicalOp);
            }
            this.CharacterAnimator.Controller.OnStumble(stumbleType, horizontalHit, verticalHit);
        }

        public void CrossFadePVPCompleteT(string characterId)
        {
            //成功!
            Sexual sexual = RG.CharacterData.Manager.Inst.GetSexual(characterId);
            this.CharacterAnimator.Controller.OnPVPFinish(true, sexual);
        }

        void HandleOnDeath()
        {
            string deathAnimation = animations.StumbleDeath;
            this._characterAnimator.Controller.OnDieByStumble();
        }

        public void HideShadow()
        {
            _shadowMR.enabled = false;
        }

        private void OnChangeIsGrounded(bool isGrounded)
        {
            if (_shadowMR != null)
                _shadowMR.enabled = isGrounded;
        }

        private void OnRoll()
        {
            StartCoroutine(OnRollPlayAnimation());
        }

        private IEnumerator OnRollPlayAnimation()
        {
            _isRolling = true;
            string rollAnimation = animations.Roll;
            string runAnimation = animations.Run;

            if (this._characterController.isGrounded)
            {
                this.CharacterAnimator.Controller.OnRoll();
            }
            else
            {
                this.CharacterAnimator.Controller.OnDive();
            }

            if (_base.BoardState.isActive)
            {
                DeActiveCurAbilityEffect(BoardAbility.Jumper);
                DeActiveAllExtraEffect();
            }

            float endTime = TimeMgr.time + 0.542f;

            while (TimeMgr.time < endTime && _isRolling)
            {
                yield return null;
            }

            EndRollAnimation();
        }

        public void EndRollAnimation()
        {
            _isRolling = false;
            _base.EndRoll();
        }

        public void OnLowriderStateChanged(bool v)
        {
            this._characterAnimator.Controller.SetLowriderState(v);
        }

        private void OverrideRollTransition()
        {

        }

        private void OnHitByTrain()
        {
            this.CharacterAnimator.Controller.OnHitByMovingTrain();
            Vector3 currentPos = _base.GamePosition;
            Vector3 camPos = GamePositionRoot.Instance.GetGamePosition(_base.CharacterCamera.MainCamera.transform);
            StartCoroutine(Utility.SetInterval(.5f, t => { _base.SetGamePosition(Vector3.Lerp(currentPos, new Vector3(camPos.x, camPos.y - 33f, currentPos.z), t)); }));
        }


        private string jumpAnimation;
        private string hangtimeAnimation;

        private void OnJetPack(bool opt)
        {

        }

        private void OnJump()
        {
            OnJump(_base.jumpHeightNormal, false);
        }

        private void OnJump(float jumpHeight, bool willPlaySound)
        {
            if (_isRolling)
            {
                EndRollAnimation();
            }

            if (_base.BoardState.isActive)
            {
                bool isSmallJump = false;
                this.CharacterAnimator.Controller.OnJump(isSmallJump);
            }
            else
            {

                bool isSmallJump = false;
                if (jumpHeight == 0 || jumpHeight > _base.jumpHeightNormal * 0.5f)
                {
                    jumpAnimation = animations.Jump;
                }
                else
                {
                    isSmallJump = true;
                    jumpAnimation = animations.SmallJump;
                }
                this.CharacterAnimator.Controller.OnJump(isSmallJump);

            }

            if (_h_AbilityEffDic.ContainsKey(BoardAbility.Jumper))
            {
                ActiveCurAbilityEffect(BoardAbility.Jumper);
            }

            _model.CharacterJump(_base.IsJumpingHigher);
        }


        private void OnRevive()
        {
            StopAllCoroutines();
            jumpAnimation = animations.Jump;
            this.CharacterAnimator.OnRevive();
        }

        private bool _queuedHangtime = false;

        public void Character_OnHangtime()
        {

            if (!_base.IsRolling)
            {
                if (!_base.BoardState.isActive || hangtimeAnimation == null)
                {
                    hangtimeAnimation = animations.Hangtime;
                }

                if (!_queuedHangtime)
                {
                    this._characterAnimator.Controller.OnFallStarted(_base.BoardState.isActive);
                }
            }
        }

        private void Character_OnHangtime_AfterAlternateRoute()
        {
            _queuedHangtime = false;
            _base.OnHangtime -= Character_OnHangtime_AfterAlternateRoute;
        }

        private void OnLanding(CharacterBase character)
        {
            string runAnimation = animations.Run;
            if (!_base.IsRolling)
            {
                if (_base.BoardState.isActive)
                {
                    string landAnimation;

                    if (_base.IsAboveGroundOfCharacterRoute)
                    {
                        runAnimation = animations.Grind("");
                        landAnimation = runAnimation + "_land";
                    }
                    else
                    {
                        landAnimation = animations.Land;
                    }

                    this.CharacterAnimator.Controller.OnLand(_base.verticalSpeed);

                    DeActiveCurAbilityEffect(BoardAbility.Jumper);
                    DeActiveAllExtraEffect();
                }
                else if (_base.verticalSpeed < -20f)
                {
                    this.CharacterAnimator.Controller.OnLand(_base.verticalSpeed);
                }
                else
                {
                    this.CharacterAnimator.Controller.OnLand(_base.verticalSpeed);
                }
            }
        }

        void OnStageMenuSequence()
        {
            StopAllCoroutines();

            _isCachedCustomizationState = _model.OutfitModel.enabled;

            _model.transform.localRotation = Quaternion.identity;
            _characterAnimator.OnReset();
            _model.StartEyeAnimations();
        }

        public void OnChangeSceneAnimationSwitch()
        {
            _model.transform.localRotation = Quaternion.identity;
        }

        internal void OnSwitchToRunningAfterAlternateRoute()
        {
            OnSwitchToRunning();

            if (!_base.BoardState.isActive)
            {
                if (!_isRolling)
                {
                    OnRun();
                }
                else
                {
                    OverrideRollTransition();
                }
            }
        }

        public void OnSwitchToRunning()
        {
            if (_h_goCached != null)
            {
                ToggleCustomHoverboard(null, null);
            }
        }


        public void OnSwitchToHoverboard(GameObject hoverBoard, BoardAbility[] abilities, RG.Board.Board board = null)
        {
            ToggleCustomHoverboard(hoverBoard, abilities, board);
            hangtimeAnimation = animations.Hangtime;
        }

        private void ToggleCustomHoverboard(GameObject newHoverboard, BoardAbility[] abilities, RG.Board.Board board = null)
        {
            if (_h_goCached != null && _h_goCached != newHoverboard)
                Destroy(_h_goCached);

            _h_goCached = newHoverboard;
            _h_Ability = abilities;
            _currentHoverboardCustomMaterials = new Material[_model.CurrentCustomBoardModels.Count];
            if (newHoverboard != null)
            {
                if (_t_Go != null)
                {
                    _t_Go.SetActive(true);

                    if (_t_Render == null)
                    {
                        _t_Render = _t_Go.GetComponentInChildren<Renderer>();
                    }

                    _t_Render.enabled = false;
                }
                if (_t_Animation != null)
                {
                    _t_Animation.normalizedTime = 0.0f;
                    _t_Animation.weight = 0.0f;
                    _t_Animation.enabled = false;
                }

                if (_t_ExtraFx != null)
                {
                    Destroy(_t_ExtraFx.gameObject);
                }

                if (_h_ExtraEffDic.Count > 0)
                {
                    foreach (var effItem in _h_ExtraEffDic)
                    {
                        if (effItem.Value != null)
                        {
                            Destroy(effItem.Value.gameObject);
                        }
                    }
                    _h_ExtraEffDic.Clear();
                }

                if (_h_AbilityEffDic.Count > 0)
                {
                    foreach (var effItem in _h_AbilityEffDic)
                    {
                        if (effItem.Value != null)
                        {
                            Destroy(effItem.Value.gameObject);
                        }
                    }
                    _h_AbilityEffDic.Clear();
                }
            }
            else
            {
                if (_t_Go != null)
                    _t_Go.SetActive(false);
            }
        }
        public void HandleRun()
        {
            this.CharacterAnimator.Controller.ForceRun();
        }

        private void SteamJumpOnStart()
        {
            _characterAnimator.Controller.OnSteamJumpStart(true);
            if (_isRolling)
            {
                EndRollAnimation();
            }

            if (_base.BoardState.isActive)
            {
                List<string> hoverboardJumps = animations.HoverboardJump;
                jumpAnimation = hoverboardJumps[0];
                hangtimeAnimation = hoverboardJumps[1];
            }
            else
            {
                jumpAnimation = animations.Jump;
                this.CharacterAnimator.Controller.OnJump(false);
            }


            if (_h_AbilityEffDic.ContainsKey(BoardAbility.Jumper))
            {
                ActiveCurAbilityEffect(BoardAbility.Jumper);
            }

            _model.CharacterJump(_base.IsJumpingHigher);
        }

        private void SteamJumpOnHangTime()
        {
            if (!_base.BoardState.isActive)
            {
                _characterAnimator.Controller.OnSteamJumpHangTime();
            }
        }

        private void SteamJumpOnStop()
        {
            _characterAnimator.Controller.OnSteamJumpStart(false);
        }

        private void IsInGame_OnChange(bool isInGame)
        {

        }

        private void SwitchToAlternateRoute(AlternateRoute route)
        {
            _queuedHangtime = true;
            ResetTeleportFx();
        }


        private void SwitchFromAlternateRoute(bool isExitingFromAir)
        {
            if (isExitingFromAir)
            {
                _base.OnHangtime += Character_OnHangtime_AfterAlternateRoute;
            }
            if (!isExitingFromAir)
            {
                OnSwitchToRunningAfterAlternateRoute();
            }
        }

        void HandleOnTeleboardStart(float duration, Vector3 offset, float gameSpeed)
        {
            if (_t_isFxActive)
            {
                StopAllCoroutines();
                ResetCharacterTeleportFX();
            }

            _isCachedCustomizationState = _model.OutfitModel.enabled;

            if (_t_ExtraFx != null)
            {
                ATeleboardEffect effect = _t_ExtraFx.GetComponent<ATeleboardEffect>();

                if (effect != null)
                {
                    effect.StartTeleBoardEffect(_base);
                }
                ObjTeleboardEffect obj = effect as ObjTeleboardEffect;
                if (obj == null)
                {
                    return;
                }
            }
            if (_t_Render != null)
                _t_Render.enabled = true;
            if (_t_Animation != null)
            {
                _t_Animation.normalizedTime = 0.0f;
                _t_Animation.weight = 0.0f;
                _t_Animation.enabled = false;
            }

            _t_isFxActive = true;

            Material modelMaterial = _model.Model.GetComponent<Renderer>().sharedMaterial;
            if (modelMaterial != teleboardFXMaterial)
                _modelMaterialCached = modelMaterial;
            _model.Model.GetComponent<Renderer>().material = teleboardFXMaterial;

            Material[] headMaterials = _model.HeadModel.sharedMaterials;
            _headMaterialsCached = new Material[headMaterials.Length];
            for (int i = 0; i < headMaterials.Length; i++)
            {
                _headMaterialsCached[i] = headMaterials[i];
                headMaterials[i] = teleboardFXMaterial;
            }
            _model.HeadModel.materials = headMaterials;

            Material customizeModelMaterial = _model.OutfitModel.GetComponent<Renderer>().sharedMaterial;
            if (customizeModelMaterial != teleboardFXMaterial)
                _customMaterialCached = customizeModelMaterial;
            _model.OutfitModel.GetComponent<Renderer>().material = teleboardFXMaterial;

            Material hoverboardMaterial = _model.CurrentHoverboard.GetComponentInChildren<Renderer>().sharedMaterial;
            if (hoverboardMaterial != teleboardFXMaterial)
                _currentHoverboardMaterial = hoverboardMaterial;

            _model.CurrentHoverboard.GetComponentInChildren<Renderer>().material = teleboardFXMaterial;

            if (_model.CurrentCustomBoardModels.Count > 0)
            {
                for (int i = 0; i < _model.CurrentCustomBoardModels.Count; i++)
                {
                    if (_model.CurrentCustomBoardModels[i] != null)//TODO: investigate why we get a null when switching project
                    {
                        Material newCustomMaterial = _model.CurrentCustomBoardModels[i].GetComponentInChildren<Renderer>().sharedMaterial;

                        if (newCustomMaterial != teleboardFXMaterial && newCustomMaterial != null)
                        {
                            _currentHoverboardCustomMaterials[i] = newCustomMaterial;
                        }
                    }
                }
                for (int i = 0; i < _model.CurrentCustomBoardModels.Count; i++)
                {
                    if (_model.CurrentCustomBoardModels[i] != null)
                    {
                        _model.CurrentCustomBoardModels[i].GetComponentInChildren<Renderer>().material = teleboardFXMaterial;
                    }
                }
            }
            StartCoroutine(TeleboardStartScale(duration, gameSpeed));
        }

        IEnumerator TeleboardStartScale(float duration, float gameSpeed)
        {
            float time = 0.0f;
            float t = 0.0f;
            float z = 0.0f;
            float endDuration = duration;

            Vector3 model1position = _base.TransformPosition;

            while (time < duration)
            {
                time += TimeMgr.deltaTime;
                t = Mathf.Clamp01(time / duration);

                if (!_t_isFxActive)
                {
                    ResetTeleportFx();
                    break;
                }

                z = _base.TransformPosition.z;

                model1position.z = z;
                _model.transform.position = model1position;
                _model.transform.localScale = new Vector3(1 - (0.5f * t), 1 + (0.5f * t), 1 - t);

                teleboardFXMaterial.SetFloat("Offset", t * 0.2f);

                if (t == 1)
                {
                    _model.transform.parent = transform;
                    _model.transform.localPosition = Vector3.zero;
                    _model.transform.localScale = Vector3.zero;
                    if (_base.IsGrounded.Value)
                        OnRun();

                    HandleOnTeleboardEnd(endDuration, gameSpeed);
                }

                yield return null;
            }
        }

        void HandleOnTeleboardEnd(float duration, float gameSpeed)
        {
            if (_base.IsGrounded.Value)
                OnRun();
            else
                Character_OnHangtime();

            if (_t_ExtraFx != null)
            {
                ATeleboardEffect effect = _t_ExtraFx.GetComponent<ATeleboardEffect>();

                if (effect != null)
                {
                    effect.EndTeleBoardEffect(_base);
                }
            }

            if (_t_Go != null)
            {
                _t_Go.transform.parent = _base.transform;
                _t_Go.transform.localPosition = Vector3.zero;
            }

            if (_t_Animation != null)
            {
                _t_Animation.normalizedTime = 1.0f;
                _t_Animation.weight = 1.0f;
                _t_Animation.speed = -(0.01f * gameSpeed);

                _t_Animation.enabled = true;
            }

            StartCoroutine(TeleboardEndScale(duration));
        }

        IEnumerator TeleboardEndScale(float duration)
        {
            float time = 0.0f;
            float t = 0.0f;
            while (time < duration)
            {
                time += TimeMgr.deltaTime;
                t = Mathf.Clamp01(time / duration);
                teleboardFXMaterial.SetFloat("Offset", t * 0.2f);

                if (t == 1)
                {
                    ResetCharacterTeleportFX();
                }
                yield return null;
            }
        }

        void ResetCharacterTeleportFX()
        {
            _model.transform.parent = transform;
            _model.transform.localPosition = Vector3.zero;
            _model.transform.localScale = Vector3.one;
            _model.Model.enabled = true;
            _model.HeadModel.enabled = true;

            _model.OutfitModel.enabled = _isCachedCustomizationState;

            if (_base.BoardState.isActive)
            {
                if (_model.CurrentHoverboard != null)
                    _model.CurrentHoverboard.SetActive(true);

                if (_model.CurrentHoverboard != null && _currentHoverboardMaterial != null)
                {
                    _model.CurrentHoverboard.GetComponentInChildren<Renderer>().material = _currentHoverboardMaterial;
                }

                if (_currentHoverboardCustomMaterials != null && _currentHoverboardCustomMaterials.Length > 0)
                {
                    for (int i = 0; i < _model.CurrentCustomBoardModels.Count; i++)
                    {
                        if (_model.CurrentCustomBoardModels[i] != null)//TODO: investigate why we get a null when switching project
                        {
                            Renderer newRenderer = _model.CurrentCustomBoardModels[i].GetComponentInChildren<Renderer>();

                            if (newRenderer != null && _currentHoverboardCustomMaterials[i] != null)
                            {
                                newRenderer.material = _currentHoverboardCustomMaterials[i];
                            }
                        }
                    }
                }
            }

            if (_modelMaterialCached != null)
            {
                _model.Model.GetComponent<Renderer>().material = _modelMaterialCached;
                _modelMaterialCached = null;
            }

            if (_headMaterialsCached != null)
            {
                _model.HeadModel.materials = _headMaterialsCached;
                _headMaterialsCached = null;
            }

            if (_customMaterialCached != null)
            {
                _model.OutfitModel.GetComponent<Renderer>().material = _customMaterialCached;
                _customMaterialCached = null;
            }

            if (_base.IsGrounded.Value)
                OnRun();

            _t_isFxActive = false;

            if (_t_ExtraFx != null)
            {
                ATeleboardEffect effect = _t_ExtraFx.GetComponent<ATeleboardEffect>();
                if (effect != null)
                {
                    effect.ResetTeleboardEffect(_model);
                }
            }
        }

        void OnApplicationPause(bool isApplicationPaused)
        {
            if (isApplicationPaused)
            {
                ResetTeleportFx();
            }
        }

        public void ResetTeleportFx()
        {
            if (_t_isFxActive)
            {
                StopAllCoroutines();
                ResetCharacterTeleportFX();
            }
        }
        #endregion

        private void RouteManager_OnRouteCreated(object sender, RouteManager.RouteCreatedEventArgs args)
        {
            AlternateRoute alternateRoute = args.Route as AlternateRoute;

            if (alternateRoute != null)
            {
                alternateRoute.OnCharacterExited += AlternateRoute_OnCharacterExited;
                alternateRoute.OnDestructing += AlternateRoute_OnDestructing;
                alternateRoute.OnCharacterEntryStarted += AlternateRoute_OnCharacterEntryStarted;
                alternateRoute.OnCharacterEntrySkipped += AlternateRoute_OnCharacterEntrySkipped;
            }
        }

        private void AlternateRoute_OnCharacterEntryStarted(object sender, Route.CharacterEntryStartedEventArgs args)
        {
            SwitchToAlternateRoute(args.ToRoute as AlternateRoute);
        }

        private void AlternateRoute_OnCharacterEntrySkipped(object sender, Route.CharacterEntrySkippedEventArgs args)
        {
            if (args.HasCharacterStartedEntryTransition)
            {
                OnRouteDone(false);
            }
        }

        private void AlternateRoute_OnCharacterExited(object sender, Route.CharacterExitedEventArgs args)
        {
            AlternateRoute alternateRoute = sender as AlternateRoute;
            alternateRoute.OnCharacterExited -= AlternateRoute_OnCharacterExited;
            alternateRoute.OnCharacterEntrySkipped -= AlternateRoute_OnCharacterEntrySkipped;
            SwitchFromAlternateRoute(true);
        }

        private void OnRouteDone(bool isRouteDestructing)
        {
            if (!isRouteDestructing)
            {
                SwitchFromAlternateRoute(false);
            }
        }

        private void AlternateRoute_OnDestructing(object sender, System.EventArgs args)
        {
            var route = (Route)sender;
            route.OnCharacterExited -= AlternateRoute_OnCharacterExited;
            route.OnDestructing -= AlternateRoute_OnDestructing;
        }

        public void ActiveCurAbilityEffect(BoardAbility ability)
        {
            if (_h_AbilityEffDic.ContainsKey(ability))
            {
                Transform effect = null;
                if (_h_AbilityEffDic.TryGetValue(ability, out effect))
                {
                    if (effect != null)
                    {
                        effect.gameObject.SetActive(false);
                        effect.gameObject.SetActive(true);
                    }
                    else
                    {
                        RGLogger.Log("Character", "effect == null,ability =" + ability.ToString());
                        _h_AbilityEffDic.Remove(ability);
                    }
                }
            }

            if (ability == BoardAbility.Jumper)
            {
                //激活跳跃特效时  隐藏通用特效
                CharacterModel.SetVanityFxPrefabActive(false);
            }
        }

        public void DeActiveCurAbilityEffect(BoardAbility ability)
        {
            if (_h_AbilityEffDic.ContainsKey(ability))
            {
                Transform effect = null;
                if (_h_AbilityEffDic.TryGetValue(ability, out effect))
                {
                    effect.gameObject.SetActive(false);
                }
            }
            if (ability == BoardAbility.Jumper)
            {
                //取消跳跃特效后 打开通用特效
                CharacterModel.SetVanityFxPrefabActive(true);
            }
        }

        public void ActiveCurExtraEffect(BoardAbility ability)
        {
            if (_h_ExtraEffDic.ContainsKey(ability))
            {
                Transform effect = null;
                if (_h_ExtraEffDic.TryGetValue(ability, out effect))
                {
                    effect.gameObject.SetActive(false);
                    effect.gameObject.SetActive(true);
                }
            }
        }

        public void DeActiveAllExtraEffect()
        {
            var enumerator = _h_ExtraEffDic.GetEnumerator();
            while (enumerator.MoveNext())
            {
                enumerator.Current.Value.gameObject.SetActive(false);
            }
        }

        #region Structs and Classes
        public class Animations
        {
            public List<string> RUN = new List<string> { "run" };
            public List<string> LAND = new List<string> { "land" };
            public List<string> BIG_LAND = new List<string> { "land" };
            public List<string> SMALL_LAND = new List<string> { "land" };
            public List<string> JUMP = new List<string> { "jump" };
            public List<string> SMALL_JUMP = new List<string> { "jump" };
            public List<string> HANGTIME = new List<string> { "hangtime" };
            public List<string> DOUBLEHANGTIME = new List<string> { "hangtime" };
            public List<string> ROLL = new List<string> { "roll" };
            public List<string> DODGE_LEFT = new List<string> { "dodgeLeft" };
            public List<string> DODGE_RIGHT = new List<string> { "dodgeRight" };
            public List<string> GRIND = new List<string> { "grind" };
            public List<string> GET_ON_BOARD = new List<string> { "getOnBoard" };
            public List<string> STUMBLEDEATH = new List<string> { "stumbleFall" };
            public List<string> SLIP = new List<string>() { "Slip01" };
            public List<string> BOOM = new List<string>() { "Boom01" };
            public List<string> BUMP_OBSTACLE = new List<string>() { "BumpObstacle01" };
            public List<string> WATER_POLO = new List<string>() { "Shuiqiu01" };
            public List<string> KAI_CHANG = new List<string>() { "Kaichang" };
            public string DEFAULT_HOVERBOARD_ANIMATION;


            public string Run
            {
                get
                {
                    return GetRandomAnimationName(RUN);
                }
            }
            public string Land { get { return GetRandomAnimationName(LAND); } }
            public string Jump { get { return GetRandomAnimationName(JUMP); } }
            public string SmallJump { get { return GetRandomAnimationName(SMALL_JUMP); } }
            public string Hangtime { get { return GetRandomAnimationName(HANGTIME); } }
            public List<string> HoverboardJump { get { return GetRandomHoverJumps(JUMP, HANGTIME); } }
            public string Roll { get { return GetRandomAnimationName(ROLL); } }
            public string DodgeLeft { get { return GetRandomAnimationName(DODGE_LEFT); } }
            public string DodgeRight { get { return GetRandomAnimationName(DODGE_RIGHT); } }
            public string Grind(string lastAnimationName) { return GetRandomAnimationNameExcluding(GRIND, lastAnimationName); }
            public string GetOnBoard
            {
                get
                {
                    return GetRandomAnimationName(GET_ON_BOARD);
                }
            }

            public string StumbleDeath { get { return GetRandomAnimationName(STUMBLEDEATH); } }

            private List<string> _animationNamesFiltered = new List<string>();
            private List<string> _animationNamesExcluded = new List<string>();


            private string GetRandomAnimationName(List<string> animationsNames)
            {
                int index = Game.Instance.RandomGenerator.Range(0, animationsNames.Count);
                return animationsNames[index];
            }


            private string GetRandomAnimationNameExcluding(List<string> animationNames, string excludeAnimationName)
            {
                _animationNamesExcluded.Clear();
                _animationNamesExcluded.Add(excludeAnimationName);
                return GetRandomAnimationNameExcluding(animationNames, _animationNamesExcluded);
            }

            private string GetRandomAnimationNameExcluding(List<string> animationNames, IEnumerable<string> excludeAnimationNames)
            {
                if (animationNames.Count == 1)
                    return animationNames[0];

                _animationNamesFiltered.Clear();

                foreach (var a in animationNames)
                {
                    if (!excludeAnimationNames.Contains(a))
                    {
                        _animationNamesFiltered.Add(a);
                    }
                }

                return GetRandomAnimationName(_animationNamesFiltered);
            }



            private List<string> GetRandomHoverJumps(List<string> hoverboardJump, List<string> hoverboardHangtime)
            {
                if (hoverboardJump.Count != hoverboardHangtime.Count)
                {
                    RGLogger.LogWarning("Character", "hoverboardJump.Count (" + hoverboardJump.Count + ") != (" + hoverboardHangtime.Count + ") hoverboardHangtime.Length");
                }
                int index = Game.Instance.RandomGenerator.Range(0, Mathf.Min(hoverboardJump.Count, hoverboardHangtime.Count));

                return new List<string> { hoverboardJump[index], hoverboardHangtime[index] };
            }
        }
        #endregion
    }
}
