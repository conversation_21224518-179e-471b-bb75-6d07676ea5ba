using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class StumblingState : CharacterState
    {
        private float _stumbleStartTime;
        private float _stumbleDuration = 0.5f;
        private bool _isStumbleEnded = false;
        private string _stumbleAniType;
        private StumbleType _stumbleType;
        private StumbleHorizontalHit _horizontalHit;
        private StumbleVerticalHit _verticalHit;
        private string _colliderName;
        private bool _safeStumble;

        public override void Enter()
        {
            _stumbleStartTime = Time.time;
            _isStumbleEnded = false;
            Character.NotifyOnStumble(_stumbleAniType, _stumbleType, _horizontalHit, _verticalHit, _colliderName, _safeStumble);
        }

        public override void Exit()
        {
            Character.StopStumble();
        }

        public override void OnStateUpdate()
        {
            // 检查绊倒是否结束
            if (!_isStumbleEnded && Time.time - _stumbleStartTime >= _stumbleDuration)
            {
                _isStumbleEnded = true;
                Character.CharacterStateManager.ChangeState<RunningState>();
            }

            // 应用重力
            Character.ApplyGravity();

            // 检查是否落地
            if (Character.IsGrounded.Value)
            {
                Character.NotifyOnLanding();
            }
        }

        public override bool CanTransitionTo(CharacterState nextState)
        {
            base.CanTransitionTo(nextState);
            if (nextState == null)
                return false;

            // 检查是否可以切换到目标状态
            if (nextState is RunningState)
            {
                return _isStumbleEnded;
            }
            else if (nextState is DeathState)
            {
                return true;
            }

            return false;
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            Debug.Assert(Character != null, "[StumblingState] Character 为空，状态未正确初始化！");
            if (Character == null)
            {
                Debug.LogError("[StumblingState] Character is null! 可能状态未初始化或被错误 new 出来。");
                return;
            }
            // 绊倒状态下不处理滑动手势
        }

        public void SetStumbleInfo(string stumbleAniType, StumbleType stumbleType, 
            StumbleHorizontalHit horizontalHit, StumbleVerticalHit verticalHit, 
            string colliderName, bool safeStumble)
        {
            _stumbleAniType = stumbleAniType;
            _stumbleType = stumbleType;
            _horizontalHit = horizontalHit;
            _verticalHit = verticalHit;
            _colliderName = colliderName;
            _safeStumble = safeStumble;
        }
    }
} 