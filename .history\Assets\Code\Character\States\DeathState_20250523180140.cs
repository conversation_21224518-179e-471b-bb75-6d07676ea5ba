using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class DeathState : CharacterState
    {
        public override void Enter()
        {
            // 关键注释：主角状态系统统一通过 _character 字段（已初始化为 Character.Instance）获取主角对象
            _character.IsDeath = true;
            _character.HandleOnDeath();
        }

        public override void Exit()
        {
            // 关键注释：主角状态系统统一通过 _character 字段（已初始化为 Character.Instance）获取主角对象
            _character.IsDeath = false;
        }

        public override void OnStateUpdate()
        {
            // 死亡状态下不进行任何更新
        }

        public override bool CanTransitionTo(CharacterState nextState)
        {
            // 死亡状态是终态，不能切换到其他状态
            return false;
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            // 死亡状态下不处理滑动手势
        }
    }
} 