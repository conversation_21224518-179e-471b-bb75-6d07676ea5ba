using UnityEngine;
using System.Collections;
using SubwayPvp.Core.Camera;
using RG.Cameras;
using RG.Inputs;
using RG.Characters;

namespace RG.Characters.States
{
    /// <summary>
    /// 角色状态基类
    /// </summary>
    public abstract class CharacterState : PvpLogicBehaviour
    {
        [SerializeField] protected CameraData _cameraData = null;
        [SerializeField] protected bool _hasEnded = false;

        protected Game _game;
        protected CharacterBase _character;
        protected CharacterCamera _characterCamera;
        protected CharacterRendering _characterRendering;
        protected CharacterController _characterController;

        protected CharacterBase Character { get { return _character; } }

        protected virtual void Reset()
        {
            _cameraData = new CameraData();
        }

        public CameraData CameraData { get { return _cameraData; } }

        /// <summary>
        /// 状态初始化（进入跑酷时由外部调用）
        /// State initialization, called when entering the game
        /// </summary>
        public virtual void InitState()
        {
            // TODO: 子类重写，做事件注册、依赖注入等
        }

        /// <summary>
        /// 状态清理（退出一局时由外部调用）
        /// State cleanup, called when exiting the game
        /// </summary>
        public virtual void ClearState()
        {
            // TODO: 子类重写，做事件注销、引用清理等
        }

        public virtual void HandleSwipe(SwipeDir swipeDir)
        {
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
        }

        public virtual IEnumerator Begin()
        {
            yield break;
        }

        public virtual IEnumerator End()
        {
            yield break;
        }

        public virtual void HandleCriticalHit() { }

        public virtual void HandleDoubleTap() { }

        public virtual void Initialize(CharacterBase character)
        {
            _character = character;
            Debug.Assert(_character != null, "CharacterState 初始化时 _character 不能为空！");
            // 允许所有 CharacterBase 子类（如 Character、Robot）都能用
            // TODO: 如果有主角专属逻辑，可在具体状态类里用 as Character 判断
        }

        /// <summary>
        /// 进入状态时调用
        /// </summary>
        public virtual void Enter() { }

        /// <summary>
        /// 退出状态时调用
        /// </summary>
        public virtual void Exit() { }

        /// <summary>
        /// 状态逻辑更新
        /// </summary>
        public virtual void OnStateUpdate() { if (!Game.Instance.IsInGame.Value) return; /* TODO: 状态系统重构后可统一由状态管理器控制 */ }

        /// <summary>
        /// 状态物理更新
        /// </summary>
        public virtual void OnStateFixedUpdate() { if (!Game.Instance.IsInGame.Value) return; /* TODO: 状态系统重构后可统一由状态管理器控制 */ }

        /// <summary>
        /// 是否可以切换到目标状态
        /// </summary>
        public virtual bool CanTransitionTo(CharacterState nextState)
        {
            return true;
        }
    }
}