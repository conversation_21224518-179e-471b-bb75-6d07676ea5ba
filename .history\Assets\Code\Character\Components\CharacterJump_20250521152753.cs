using UnityEngine;
using System.Collections;
using RG.Characters;

namespace Assets.Code.Character.Components
{
    public class CharacterJump : MonoBehaviour
    {
        private CharacterBase _character;
        private CharacterMovement _movement;
        private bool _willSecondJump;
        private bool _inAirJump;

        public void Initialize(CharacterBase character, CharacterMovement movement)
        {
            _character = character;
            _movement = movement;
        }

        public bool PerformJump()
        {
            bool forgivenJump = !_character.IsJumping && _movement.VerticalSpeed <= 0f && _movement.VerticalSpeed > _character.CharacterConstant.JUMP_VERTICAL_SPEED;
            bool canJump = _character.CharacterController.isGrounded || forgivenJump;

            if (canJump)
            {
                if (_character.IsJumping && _character.JumpUpperCollisionEvaded)
                    _character.JumpUpperCollision = false;

                if (_character.IsRolling)
                {
                    _character.ExpandColliders();
                }
                _character.GlideTimeLeft = 0.0f;
                _character.VTolEnded = false;

                _willSecondJump = true;
                _character.IsJumping = true;
                _character.IsFalling = false;

                _character.IsGrounded.Value = false;

                if (_character.IsJumpingHigher)
                {
                    Vector3 position = _character.GamePosition;
                    _character.SuperJumpAltitude = _character.SuperJumpOriginal;

                    var jump = new SuperSneakersJump();
                    jump.StartGamePositionZ = position.z;
                    jump.Length = _character.GetJumpDistance(_character.CurrentSpeed, _character.SuperJumpAltitude) * _character.SuperSneakersJumpApexRatio;
                    jump.EndGamePositionZ = jump.StartGamePositionZ + jump.Length;
                    jump.StartGamePositionY = position.y;

                    _character.SuperSneakersJump = jump;
                    _movement.VerticalSpeed = 0f;
                }
                else
                {
                    _movement.VerticalSpeed = _character.CalculateJumpVerticalSpeed(_character.JumpHeight);
                }

                _character.NotifyOnJump(_character.IsJumpingHigher ? _character.SuperJumpAltitude : _character.JumpHeightNormal, false);
                if (_character.IsRunningOnGround())
                {
                    _character.StartedJumpFromGround = true;
                    _character.TrainJump = false;
                    _character.TrainJumpSampleZ = _character.GamePosition.z + _character.CharacterConstant.JUMP_TRAIN_DISTANCE;
                }
            }
            else
            {
                if (_movement.VerticalSpeed < 0f)
                {
                    _inAirJump = true;
                }
            }
            return canJump;
        }

        public void UpdateJumpState()
        {
            if (_character.IsJumping && _character.JumpUpperCollisionEvaded)
            {
                _character.JumpUpperCollision = false;
            }

            if (_character.IsFalling && _character.IsGliderboard)
            {
                if (_character.GlideTimeLeft < _character.GlideTime)
                {
                    _movement.VerticalSpeed = _character.GliderGravity * _character.GlideVerticalSpeed.Evaluate(_character.GlideTimeLeft / _character.GlideTime);
                    _character.GlideTimeLeft += Time.deltaTime;
                }
                else
                {
                    _movement.VerticalSpeed -= _character.GliderGravity * Time.deltaTime;
                }
            }
            else
            {
                _movement.VerticalSpeed -= _character.Gravity * Time.deltaTime;
            }

            if (!_character.CharacterController.isGrounded)
            {
                if (!_character.IsFalling && _movement.VerticalSpeed < _character.VerticalFallSpeedLimit && !_character.IsRolling)
                {
                    _character.IsFalling = true;
                    _character.SendHangtimeEvent();
                    _character.IsGrounded.Value = false;
                }
            }
        }

        public bool WillSecondJump
        {
            get { return _willSecondJump; }
            set { _willSecondJump = value; }
        }

        public bool InAirJump
        {
            get { return _inAirJump; }
            set { _inAirJump = value; }
        }
    }
} 