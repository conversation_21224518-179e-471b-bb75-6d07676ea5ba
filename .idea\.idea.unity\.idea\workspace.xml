<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bbf400db-4aa4-4276-ae7c-848b34015087" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Dummy.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ca42919df2b74e53b11002749c8755af118a00/8d/70c031dc/SetupCoroutine.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ca42919df2b74e53b11002749c8755af118a00/b5/31bec7df/PlayerPrefs.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/ca42919df2b74e53b11002749c8755af118a00/be/652e7f22/Time.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Code/Common/GameConfig.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.addressables@1.21.9/Runtime/ResourceManager/Diagnostics/DiagnosticEventCollector.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.editorcoroutines@1.0.0/Editor/EditorCoroutineUtility.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.multiplayer-hlapi@1.0.8/Runtime/ChannelBuffer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.textmeshpro@2.1.4/Scripts/Editor/TMP_BaseShaderGUI.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2v4gVWvco3rVbGBmwOnDqXUZggF" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor &amp; Play.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;rider.external.source.directories&quot;: [
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\DecompilerCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\SourcesCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Local\\Symbols\\src&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor &amp; Play">
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Program Files\Unity\Hub\2019.4.40f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath G:\subway\new_pvp_client\trunk\unity -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\subway\new_pvp_client\trunk\unity" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Program Files\Unity\Hub\2019.4.40f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath G:\subway\new_pvp_client\trunk\unity -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\subway\new_pvp_client\trunk\unity" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost" ignored-value-for-modified-check="69192">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" value="69192" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bbf400db-4aa4-4276-ae7c-848b34015087" name="Changes" comment="" />
      <created>1743409784016</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743409784016</updated>
      <workItem from="1743409785089" duration="2264000" />
      <workItem from="1744183830274" duration="20588000" />
      <workItem from="1745568079744" duration="1900000" />
      <workItem from="1747128054614" duration="41845000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Module/Common/OnlineSettings/PopupSetting.cs</url>
          <line>107</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Module\Common\OnlineSettings\PopupSetting.cs" containingFunctionPresentation="Method 'UpdateInfos'">
            <startOffsets>
              <option value="4155" />
            </startOffsets>
            <endOffsets>
              <option value="4207" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Module/Common/OnlineSettings/PopupSetting.cs</url>
          <line>65</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Module\Common\OnlineSettings\PopupSetting.cs" containingFunctionPresentation="Method 'UpdateInfos'">
            <startOffsets>
              <option value="2095" />
            </startOffsets>
            <endOffsets>
              <option value="2144" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Module/Common/OnlineSettings/OnlineSettingMgr.cs</url>
          <line>30</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Module\Common\OnlineSettings\OnlineSettingMgr.cs" containingFunctionPresentation="Method 'ParseAllItems'">
            <startOffsets>
              <option value="809" />
            </startOffsets>
            <endOffsets>
              <option value="845" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Localization/LocalizationMgr.cs</url>
          <line>94</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Localization\LocalizationMgr.cs" containingFunctionPresentation="Lambda expression inside Method '_Load'">
            <startOffsets>
              <option value="3178" />
            </startOffsets>
            <endOffsets>
              <option value="3245" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Character/States/CharacterStateManager.cs</url>
          <line>31</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Character\States\CharacterStateManager.cs" containingFunctionPresentation="Method 'RegisterState'">
            <startOffsets>
              <option value="855" />
            </startOffsets>
            <endOffsets>
              <option value="884" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Character/States/CharacterStateManager.cs</url>
          <line>57</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Character\States\CharacterStateManager.cs" containingFunctionPresentation="Method 'GetState'">
            <startOffsets>
              <option value="1591" />
            </startOffsets>
            <endOffsets>
              <option value="1624" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Character/States/CharacterStateManager.cs</url>
          <line>39</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Character\States\CharacterStateManager.cs" containingFunctionPresentation="Method 'ChangeState'">
            <startOffsets>
              <option value="1053" />
            </startOffsets>
            <endOffsets>
              <option value="1090" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Code/Character/States/JumpingState.cs</url>
          <line>67</line>
          <properties documentPath="G:\subway\new_pvp_client\trunk\unity\Assets\Code\Character\States\JumpingState.cs" containingFunctionPresentation="Method 'CanTransitionTo'">
            <startOffsets>
              <option value="1934" />
            </startOffsets>
            <endOffsets>
              <option value="2001" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>