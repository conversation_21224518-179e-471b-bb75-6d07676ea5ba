2025-04-18 15:21:00 | INFO | autocoder.common.file_monitor.monitor | FileMonitor singleton initialized for root directory: G:\subway\new_pvp_client\trunk\unity
2025-04-18 15:21:00 | INFO | autocoder.common.file_monitor.monitor | Starting file monitor...
2025-04-18 15:21:00 | INFO | autocoder.common.file_monitor.monitor | File monitor loop started for G:\subway\new_pvp_client\trunk\unity...
2025-04-18 15:21:00 | INFO | autocoder.common.file_monitor.monitor | File monitor started in background thread.
2025-04-18 15:25:27 | INFO | autocoder.common.file_monitor.monitor | FileMonitor singleton initialized for root directory: G:\subway\new_pvp_client\trunk\unity
2025-04-18 15:25:27 | INFO | autocoder.common.file_monitor.monitor | Starting file monitor...
2025-04-18 15:25:27 | INFO | autocoder.common.file_monitor.monitor | File monitor loop started for G:\subway\new_pvp_client\trunk\unity...
2025-04-18 15:25:27 | INFO | autocoder.common.file_monitor.monitor | File monitor started in background thread.
2025-04-18 15:29:38 | INFO | byzerllm.utils.client.simple_byzerllm_client | deploy_info: {'model_path': '', 'pretrained_model_type': 'saas/openai', 'infer_params': {'saas.base_url': 'https://api.deepseek.com/v1', 'saas.api_key': '***********************************', 'saas.model': 'deepseek-chat', 'saas.is_reasoning': False, 'saas.max_output_tokens': 8096}, 'sync_client': <openai.OpenAI object at 0x000001FD2F67B640>, 'async_client': <openai.AsyncOpenAI object at 0x000001FD2F84FD30>, 'model': 'deepseek-chat', 'is_reasoning': False, 'max_output_tokens': 8096}
