using UnityEngine;
using System.Collections;
using RG.Common;
using RG.Routes;

namespace RG.Characters.Components
{
    public class CharacterTrack : MonoBehaviour
    {
        private CharacterBase _character;
        private int _trackIndex;
        private int _trackIndexTarget;
        private float _trackIndexPosition;
        private float _sameLaneTimeStamp;
        private int _trackMovement;
        private int _trackMovementNext;
        private int _ignoredMovement;

        public void Initialize(CharacterBase character)
        {
            _character = character;
            _trackIndex = character._initialTrackIndex;
            _trackIndexTarget = character._initialTrackIndex;
        }

        public void ChangeTrack(int trackDelta, float duration)
        {
            _sameLaneTimeStamp = Time.time;

            if (_trackMovement != trackDelta)
            {
                ForceChangeTrack(trackDelta, duration);

                if (_character.IsTeleboard)
                {
                    float tSpeed = _character.CurrentSpeed;
                    if (_character.CurrentSpeed < RouteConstants.WIDTH_PER_LANE)
                    {
                        tSpeed = RouteConstants.WIDTH_PER_LANE;
                    }

                    if (trackDelta == 1)
                    {
                        float durationex = 17f / tSpeed;
                        _character.SendTeleboardEvent(new Vector3(20.0f, 0.0f, 1.0f), durationex, _character.CurrentSpeed);
                    }
                    else if (trackDelta == -1)
                    {
                        float durationex = 17f / tSpeed;
                        _character.SendTeleboardEvent(new Vector3(-20.0f, 0.0f, 1.0f), durationex, _character.CurrentSpeed);
                    }
                }
            }
            else
            {
                _trackMovementNext = trackDelta;
            }
        }

        public void ForceChangeTrack(int movement, float duration)
        {
            _character.StopAllCoroutines();
            _character.StartCoroutine(ChangeTrackCoroutine(movement, duration));
        }

        public void InitialTrackIndex(int index)
        {
            _trackIndex = index;
            _character.CurrentRoadIndex = index;
            _trackIndexTarget = index;
            _character.x = Route.GetTrackX(_character.CurrentRoadIndex);
            Vector3 xPositionTarget = Route.GetPosition(_character.x, 0);
            _character.CharacterController.Move(xPositionTarget);
            _character.CachePositions();
        }

        private IEnumerator ChangeTrackCoroutine(int movement, float duration)
        {
            float startTime = Time.time;
            float startX = _character.x;
            float targetX = Route.GetTrackX(_character.CurrentRoadIndex + movement);

            while (Time.time - startTime < duration)
            {
                float t = (Time.time - startTime) / duration;
                _character.x = Mathf.Lerp(startX, targetX, t);
                yield return null;
            }

            _character.x = targetX;
            _character.CurrentRoadIndex += movement;
            _trackMovement = 0;
            _character.SendChangeTrackEvent(movement > 0 ? OnChangeTrackDirection.Right : OnChangeTrackDirection.Left);
        }

        public int GetCurrentTrackIndex()
        {
            return _trackIndex;
        }

        public int GetTrackMovement()
        {
            return _trackMovement;
        }
    }
} 