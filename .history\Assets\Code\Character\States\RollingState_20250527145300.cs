using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using RG.Cameras;
using RG;
using RG.Characters;
using RG.Routes;
using RG.Common;
using RG.CharacterData;
using RG.Common.Logger;

namespace RG.Characters.States
{
    public class RollingState : CharacterState
    {
        private float _rollStartTime;
        private float _rollDuration = 0.5f;
        private bool _isRollingEnded = false;

        public override void Enter()
        {
            _rollStartTime = Time.time;
            _isRollingEnded = false;
            Character.IsRolling = true;
            // 关键注释：主角状态系统统一通过 _character 字段（已初始化为 Character.Instance）获取主角对象
            Character.SendRollEvent();
        }

        public override void Exit()
        {
            Character.IsRolling = false;
            Character.EndRoll();
        }

        public override void OnStateUpdate()
        {
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
            // 检查翻滚是否结束
            if (!_isRollingEnded && Time.time - _rollStartTime >= _rollDuration)
            {
                _isRollingEnded = true;
                Character.CharacterStateManager.ChangeState<RunningState>();
            }

            // 应用重力
            Character.ApplyGravity();

            // 检查是否落地
            if (Character.IsGrounded.Value)
            {
                Character.NotifyOnLanding();
            }
        }

        public override bool CanTransitionTo(CharacterState nextState)
        {
            base.CanTransitionTo(nextState);
            if (nextState == null)
                return false;

            // 检查是否可以切换到目标状态
            if (nextState is RunningState)
            {
                return _isRollingEnded;
            }
            else if (nextState is DeathState)
            {
                return true;
            }

            return false;
        }

        public override void HandleSwipe(SwipeDir swipeDir)
        {
            Debug.Assert(Character != null, "[RollingState] Character 为空，状态未正确初始化！");
            if (Character == null)
            {
                Debug.LogError("[RollingState] Character is null! 可能状态未初始化或被错误 new 出来。");
                return;
            }
            if (!Game.Instance.IsInGame.Value) return; // TODO: 状态系统重构后可统一由状态管理器控制
            // TODO: 重构后优化此处，兼容主角单例
            if (Character.isStopRunning) return;

            float currentSpeed = Character.CurrentSpeed;
            if (currentSpeed < RouteConstants.WIDTH_PER_LANE)
            {
                currentSpeed = RouteConstants.WIDTH_PER_LANE;
            }

            switch (swipeDir)
            {
                case SwipeDir.None:
                    break;
                case SwipeDir.Left:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(-1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(-1, RunningState.CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
                case SwipeDir.Right:
                    if (Character.IsTeleboard)
                    {
                        Character.ChangeTrackBy(+1, RouteConstants.WIDTH_PER_LANE / currentSpeed);
                    }
                    else
                    {
                        Character.ChangeTrackBy(+1, RunningState.CHARACTER_CHANGE_TRACK_LENGTH / currentSpeed);
                    }
                    break;
            }
        }

        // 状态初始化（进入跑酷时由外部调用）
        public override void InitState()
        {
            // TODO: 事件注册、依赖注入等
        }

        // 状态清理（退出一局时由外部调用）
        public override void ClearState()
        {
            // TODO: 事件注销、引用清理等
        }
    }
} 