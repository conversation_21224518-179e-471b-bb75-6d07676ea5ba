---
description: 
globs: *.cs
alwaysApply: false
---
# C#代码风格指南 (C# Code Style Guide)

## 命名约定 (Naming Conventions)

### 类型命名 (Type Names)
- 类名使用PascalCase: `PlayerController`, `GameManager`
- 接口以I开头: `IInteractable`, `IDataProvider`
- 抽象类可以以Base开头: `BaseCharacter`, `BaseState`

### 成员命名 (Member Names)
- 公共属性和方法使用PascalCase: `Transform`, `GetComponent`
- 私有字段使用camelCase并以_开头: `_health`, `_playerData`
- 常量使用全大写SNAKE_CASE: `MAX_HEALTH`, `PLAYER_SPEED`

### 参数命名 (Parameter Names)
- 参数使用camelCase: `playerHealth`, `moveSpeed`
- 避免单字母参数名，除非在lambda表达式中

## 代码格式 (Code Formatting)

### 缩进和空格 (Indentation and Spacing)
```csharp
public class ExampleClass
{
    private int _value;

    public void ExampleMethod()
    {
        if (condition)
        {
            DoSomething();
        }
    }
}
```

### 大括号使用 (Braces Usage)
- 总是使用大括号，即使是单行if语句
- 大括号单独占一行
- 避免内联大括号

### 注释规范 (Comment Style)
```csharp
/// <summary>
/// 玩家控制器类 (Player Controller Class)
/// </summary>
public class PlayerController : MonoBehaviour
{
    // 私有变量 (Private variables)
    private float _moveSpeed;

    /// <summary>
    /// 处理玩家移动 (Handle player movement)
    /// </summary>
    private void HandleMovement()
    {
        // 实现移动逻辑 (Implement movement logic)
    }
}
```

## Unity特定规范 (Unity-Specific Guidelines)

### MonoBehaviour方法顺序 (MonoBehaviour Method Order)
1. [SerializeField] 变量
2. 私有变量
3. Unity消息函数 (Awake, Start, Update等)
4. 公共方法
5. 私有方法
6. 协程

### 序列化字段 (Serialized Fields)
```csharp
public class WeaponController : MonoBehaviour
{
    [SerializeField] 
    private float _damage = 10f;

    [Header("武器设置 (Weapon Settings)")]
    [SerializeField] 
    private float _fireRate = 0.5f;
}
```

### 组件引用 (Component References)
```csharp
public class Example : MonoBehaviour
{
    [SerializeField]
    private Rigidbody _rigidbody;

    private void Awake()
    {
        // 如果未在Inspector中赋值，则自动获取
        _rigidbody ??= GetComponent<Rigidbody>();
    }
}
```

## 性能考虑 (Performance Considerations)

### 缓存优化 (Caching)
```csharp
// 推荐方式
private Transform _transform;

private void Awake()
{
    _transform = transform;
}

// 避免在Update中频繁获取组件
private void Update()
{
    _transform.position += Vector3.forward;
}
```

### 字符串处理 (String Handling)
- 使用StringBuilder进行字符串拼接
- 常量字符串使用const声明
- 避免在Update中进行字符串操作

## 错误处理 (Error Handling)

### 异常处理 (Exception Handling)
```csharp
public void LoadData()
{
    try
    {
        // 加载数据 (Load data)
        LoadFromFile();
    }
    catch (FileNotFoundException ex)
    {
        Debug.LogError($"文件未找到 (File not found): {ex.Message}");
    }
    catch (Exception ex)
    {
        Debug.LogError($"加载失败 (Loading failed): {ex.Message}");
    }
}
```

### 空值检查 (Null Checking)
```csharp
// 使用null条件运算符
player?.TakeDamage(10);

// 使用null合并运算符
_config = LoadConfig() ?? DefaultConfig;
```

## 网络通信规范 (Network Communication Guidelines)

### 通信架构 (Communication Architecture)
```csharp
// 推荐架构
public class NetworkManager : MonoBehaviour 
{
    private static NetworkManager _instance;
    public static NetworkManager Inst => _instance;

    // 网络状态
    private NetworkState _state;
    
    // 事件系统
    public event Action<NetworkState> OnStateChanged;
    
    // 初始化
    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
        }
    }
}
```

### 通信方式选择 (Communication Method Selection)
1. HTTP请求:
   - 用于非实时数据更新(用户信息、配置等)
   - 使用统一的HTTP管理器
   ```csharp
   Common.HTTP.Post(url, (result) => {
       if (result.ResultCode == 0) {
           // 处理成功响应
       }
   });
   ```

2. Photon网络:
   - 用于实时游戏数据同步
   - 使用事件系统进行通信
   ```csharp
   PhotonNetwork.RaiseEvent(eventCode, data, reliable, options);
   ```

### 错误处理 (Error Handling)
```csharp
// 网络断开处理
public void OnDisconnected()
{
    // 1. 记录日志
    Debug.LogWarning("网络连接断开");
    
    // 2. 通知UI
    UIManager.ShowNetworkError();
    
    // 3. 尝试重连
    StartCoroutine(ReconnectRoutine());
}

// 重连机制
private IEnumerator ReconnectRoutine()
{
    while (!IsConnected)
    {
        yield return new WaitForSeconds(3f);
        TryReconnect();
    }
}
```

### 数据同步 (Data Synchronization)
```csharp
// 1. 使用序列化接口
public class NetworkData : ISerializable
{
    public void Serialize(PhotonStream stream)
    {
        // 序列化数据
    }
    
    public void Deserialize(PhotonStream stream)
    {
        // 反序列化数据
    }
}

// 2. 状态同步
public class NetworkState
{
    public bool IsConnected { get; private set; }
    public float Latency { get; private set; }
    
    public void UpdateState(bool isConnected, float latency)
    {
        IsConnected = isConnected;
        Latency = latency;
        OnStateChanged?.Invoke(this);
    }
}
```

### 安全性 (Security)
1. 身份验证:
```csharp
// 使用token进行身份验证
public void Authenticate(string token)
{
    var authData = new Dictionary<string, object>
    {
        { "token", token },
        { "timestamp", DateTime.Now.Ticks }
    };
    
    PhotonNetwork.AuthValues = new AuthenticationValues
    {
        AuthType = CustomAuthenticationType.Custom,
        AuthParameters = authData
    };
}
```

2. 数据加密:
```csharp
// 启用加密
PhotonNetwork.NetworkingPeer.EncryptionMode = EncryptionMode.PayloadEncryption;
```

### 性能优化 (Performance Optimization)
1. 消息压缩:
```csharp
// 使用压缩
PhotonNetwork.NetworkingPeer.CompressionLevel = CompressionLevel.Low;
```

2. 批量处理:
```csharp
// 批量发送数据
public void BatchSendData(List<NetworkData> dataList)
{
    var batch = new List<object>();
    foreach (var data in dataList)
    {
        batch.Add(data.Serialize());
    }
    PhotonNetwork.RaiseEvent(EventCode.BatchData, batch, true);
}
```

### 调试与日志 (Debugging and Logging)
```csharp
// 网络日志
public static class NetworkLogger
{
    public static void LogNetworkEvent(string eventName, object data)
    {
        #if UNITY_EDITOR
        Debug.Log($"[Network] {eventName}: {JsonUtility.ToJson(data)}");
        #endif
    }
    
    public static void LogNetworkError(string error, Exception ex = null)
    {
        Debug.LogError($"[Network Error] {error}");
        if (ex != null)
        {
            Debug.LogException(ex);
        }
    }
}
```

### 最佳实践 (Best Practices)
1. 网络状态检查:
```csharp
// 发送前检查网络状态
public void SendData(object data)
{
    if (!PhotonNetwork.IsConnected)
    {
        NetworkLogger.LogNetworkError("网络未连接");
        return;
    }
    
    PhotonNetwork.RaiseEvent(EventCode.Data, data, true);
}
```

2. 超时处理:
```csharp
// 请求超时处理
public IEnumerator RequestWithTimeout(string url, float timeout)
{
    var request = UnityWebRequest.Get(url);
    var operation = request.SendWebRequest();
    
    float startTime = Time.time;
    while (!operation.isDone)
    {
        if (Time.time - startTime > timeout)
        {
            request.Abort();
            yield break;
        }
        yield return null;
    }
}
```

3. 重试机制:
```csharp
// 失败重试
public async Task<bool> RetryOperation(Func<Task<bool>> operation, int maxRetries = 3)
{
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            if (await operation())
                return true;
        }
        catch (Exception ex)
        {
            NetworkLogger.LogNetworkError($"重试 {i + 1}/{maxRetries} 失败", ex);
        }
        await Task.Delay(1000 * (i + 1));
    }
    return false;
}
```

## 资源加载系统规范 (Resource Loading System Guidelines)

### 1. 资源加载架构 (Resource Loading Architecture)
```csharp
// 资源管理器基类 (Resource Manager Base Class)
public abstract class ResourceManager
{
    protected Dictionary<string, object> _cache;
    
    public abstract Task<T> LoadAsync<T>(string path) where T : UnityEngine.Object;
    public abstract void Unload(string path);
}

// 具体资源管理器实现 (Concrete Resource Manager Implementation)
public class AddressableManager : ResourceManager 
{
    public override async Task<T> LoadAsync<T>(string path)
    {
        // 实现Addressable加载逻辑 (Implement Addressable loading logic)
    }
}
```

### 2. 资源加载优先级 (Resource Loading Priority)
1. 本地缓存 (Local Cache)
2. Resources文件夹 (Resources Folder)
3. Addressable远程加载 (Addressable Remote Loading)
4. AssetBundle远程加载 (AssetBundle Remote Loading)

### 3. 异步加载规范 (Async Loading Guidelines)
```csharp
// 推荐写法 (Recommended)
public async Task<T> LoadResourceAsync<T>(string path) where T : UnityEngine.Object
{
    try 
    {
        var result = await LoadAsync<T>(path);
        return result;
    }
    catch (Exception e)
    {
        Debug.LogError($"Failed to load resource: {path}, error: {e}");
        return null;
    }
}

// 不推荐写法 (Not Recommended)
public void LoadResource(string path, Action<object> callback)
{
    // 避免使用回调方式 (Avoid using callbacks)
}
```

### 4. 缓存管理 (Cache Management)
```csharp
public class ResourceCache
{
    private Dictionary<string, WeakReference> _cache;
    
    public void Add(string key, object value)
    {
        _cache[key] = new WeakReference(value);
    }
    
    public void ClearUnused()
    {
        // 清理未使用的资源 (Clear unused resources)
    }
}
```

### 5. 版本控制 (Version Control)
```csharp
public class ResourceVersionControl
{
    private Dictionary<string, int> _versions;
    
    public bool CheckUpdate(string resourceId)
    {
        // 检查资源版本 (Check resource version)
        return _versions[resourceId] < GetRemoteVersion(resourceId);
    }
}
```

### 6. 错误处理 (Error Handling)
```csharp
public class ResourceLoader
{
    private const int MAX_RETRY_COUNT = 3;
    
    public async Task<T> LoadWithRetry<T>(string path) where T : UnityEngine.Object
    {
        int retryCount = 0;
        while (retryCount < MAX_RETRY_COUNT)
        {
            try
            {
                return await LoadAsync<T>(path);
            }
            catch (Exception e)
            {
                retryCount++;
                if (retryCount >= MAX_RETRY_COUNT)
                    throw;
                await Task.Delay(1000 * retryCount);
            }
        }
        return null;
    }
}
```

### 7. 资源分组 (Resource Grouping)
```csharp
public enum ResourceGroup
{
    UI,
    Audio,
    Model,
    Texture
}

public class ResourceGroupManager
{
    private Dictionary<ResourceGroup, ResourceManager> _managers;
    
    public ResourceManager GetManager(ResourceGroup group)
    {
        return _managers[group];
    }
}
```

### 8. 性能优化 (Performance Optimization)
1. 使用对象池 (Use Object Pooling)
2. 实现资源预加载 (Implement Resource Preloading)
3. 异步加载避免阻塞 (Avoid Blocking with Async Loading)
4. 合理使用缓存 (Use Cache Appropriately)
5. 及时释放未使用资源 (Release Unused Resources Promptly)

### 9. 调试与日志 (Debugging and Logging)
```csharp
public class ResourceLogger
{
    public static void LogLoad(string path, float time)
    {
        Debug.Log($"Resource loaded: {path}, time: {time}ms");
    }
    
    public static void LogError(string path, Exception e)
    {
        Debug.LogError($"Failed to load: {path}, error: {e}");
    }
}
```

### 10. 最佳实践 (Best Practices)
1. 资源命名规范 (Resource Naming Conventions)
   - 使用小写字母 (Use lowercase letters)
   - 使用下划线分隔 (Use underscores as separators)
   - 包含资源类型前缀 (Include resource type prefix)

2. 路径管理 (Path Management)
   - 使用常量定义基础路径 (Use constants for base paths)
   - 避免硬编码路径 (Avoid hardcoded paths)
   - 统一路径分隔符 (Use consistent path separators)

3. 内存管理 (Memory Management)
   - 及时释放未使用资源 (Release unused resources promptly)
   - 监控内存使用 (Monitor memory usage)
   - 实现内存预警机制 (Implement memory warning mechanism)

4. 加载策略 (Loading Strategy)
   - 根据资源类型选择加载方式 (Choose loading method based on resource type)
   - 实现资源依赖管理 (Implement resource dependency management)
   - 支持加载优先级 (Support loading priorities)

5. 错误恢复 (Error Recovery)
   - 实现加载失败重试 (Implement loading failure retry)
   - 提供降级加载方案 (Provide fallback loading solution)
   - 记录错误日志 (Log error information)

## 日志系统规范 (Logging System Guidelines)

### 1. 日志级别使用 (Log Level Usage)
```csharp
// 普通日志 - 用于记录一般信息
RGLogger.Log("UI", "界面加载成功");

// 警告日志 - 用于记录需要注意但不影响程序运行的问题
RGLogger.LogWarning("Resource", "资源加载超时");

// 错误日志 - 用于记录影响功能但不会导致程序崩溃的问题
RGLogger.LogError("UI", $"ConfigId: {configId}, 当前队列数量: {queueCount}");

// 异常日志 - 用于记录程序异常
try {
    // 某些操作
} catch (Exception e) {
    RGLogger.LogException("System", e);
}

// 断言日志 - 用于调试和验证
RGLogger.Assert("Game", player != null, "玩家对象不能为空");
```

### 2. 日志标签规范 (Log Tag Conventions)
- UI: 界面相关日志
- Resource: 资源加载相关日志
- Network: 网络通信相关日志
- Game: 游戏逻辑相关日志
- System: 系统相关日志

### 3. 日志内容规范 (Log Content Guidelines)
```csharp
// 推荐写法
RGLogger.LogError("UI", $"ConfigId: {configId}, 当前队列数量: {queueCount}, 历史堆栈数量: {stackCount}");

// 不推荐写法
RGLogger.LogError("UI", "加载失败"); // 信息不完整
```

### 4. 日志开关控制 (Log Control)
```csharp
// 运行时控制日志输出
RGLogger.SetLogEnabled(false);      // 关闭普通日志
RGLogger.SetWarningEnabled(true);   // 开启警告日志
RGLogger.SetErrorEnabled(true);     // 开启错误日志
RGLogger.SetExceptionEnabled(true); // 开启异常日志
RGLogger.SetAssertEnabled(false);   // 关闭断言日志

// 批量控制
RGLogger.EnableAllLogs();    // 开启所有日志
RGLogger.DisableAllLogs();   // 关闭所有日志
```

### 5. 日志过滤 (Log Filtering)
```csharp
// 添加过滤器
RGLogger.AddLogFilter("UI");        // 只显示UI标签的普通日志
RGLogger.AddErrorFilter("Network"); // 只显示Network标签的错误日志

// 移除过滤器
RGLogger.RemoveLogFilter("UI");
RGLogger.RemoveErrorFilter("Network");

// 清除所有过滤器
RGLogger.ClearAllFilters();
```

### 6. 日志颜色定制 (Log Color Customization)
```csharp
// 设置日志标签颜色
RGLogger.SetLogColor("UI", Color.cyan);
RGLogger.SetLogColor("Resource", Color.yellow);
```

### 7. 最佳实践 (Best Practices)
1. 日志内容：
   - 包含足够的上下文信息
   - 使用结构化的信息格式
   - 避免敏感信息泄露

2. 日志级别选择：
   - Log: 用于记录正常流程信息
   - Warning: 用于记录需要注意但不影响功能的问题
   - Error: 用于记录影响功能但不会导致崩溃的问题
   - Exception: 用于记录程序异常
   - Assert: 用于调试和验证

3. 性能考虑：
   - 避免在频繁调用的方法中输出日志
   - 使用条件编译控制日志输出
   - 合理使用日志过滤器

4. 发布版本：
   - 默认关闭普通日志
   - 保留错误和异常日志
   - 关闭断言日志
   - 可通过配置文件控制日志开关
