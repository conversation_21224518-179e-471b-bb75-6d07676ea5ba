using UnityEngine;

public class Hoverboard : MonoBehaviour
{
    private static Hoverboard _instance = null;
    public static Hoverboard Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType(typeof(Hoverboard)) as Hoverboard;
            }
            return _instance;
        }
    }

    // 添加初始化状态和事件
    private bool _isInitialized = false;
    public bool IsInitialized => _isInitialized;
    public event System.Action OnInitialized;

    public void Initialize()
    {
        if (_isInitialized) return;
        
        InitValue();
        RegisterGameEvent();
        InitModel();
        
        _isInitialized = true;
        OnInitialized?.Invoke();
    }

    private void InitValue()
    {
        _game = Game.Instance;
        _characterController = Character.Instance.CharacterController;
        _steamJumpState = SteamJumpState.Instance;
        _camera = CharacterCamera.Instance;
        _routeManager = RouteManager.Instance;
    }

    private void RegisterGameEvent()
    {
        _routeManager.OnRouteCreated += RouteManager_OnRouteCreated;
        Character.Instance.OnChangeTrack += OnChangeTrack;
        Character.Instance.OnStumble += OnStumble;
        Character.Instance.OnRevive += OnRevive;
        Character.Instance.OnResetTeleportFX += ResetCharacterTeleportFX;
        Character.Instance.OnHitByTrain += OnHitByTrain;
        Character.Instance.OnJump += OnJump;
        Character.Instance.OnJumpIfHitByTrain += OnJump;
        Character.Instance.OnRoll += OnRoll;
        Character.Instance.OnHangtime += Character_OnHangtime;
        Character.Instance.IsGrounded.OnChange += OnChangeIsGrounded;
        Character.Instance.OnTeleboardStart += HandleOnTeleboardStart;
        Character.Instance.OnRouteDone += OnRouteDone;
        Character.Instance.OnLanding += OnLanding;

        Character.Instance.BoardState.OnJump += OnJump;
        Character.Instance.BoardState.OnRun += OnRun;
        Character.Instance.BoardState.OnEndHoverboard += OnSwitchToRunning;

        Character.Instance.OnJetPack += OnJetPack;

        InitializeCharacterRenderingEffects();

        Game.OnMenu += OnStageMenuSequence;
        Game.OnIntro += OnIntroRun;
        _game.IsInGame.OnChange += IsInGame_OnChange;
        _steamJumpState.OnStart += SteamJumpOnStart;
        _steamJumpState.OnHangTime += SteamJumpOnHangTime;
        _steamJumpState.OnStop += SteamJumpOnStop;
    }

    private void InitModel()
    {
        // Implementation of InitModel method
    }

    private void RouteManager_OnRouteCreated(Route route)
    {
        // Implementation of RouteManager_OnRouteCreated method
    }

    private void OnChangeTrack(Track track)
    {
        // Implementation of OnChangeTrack method
    }

    private void OnStumble()
    {
        // Implementation of OnStumble method
    }

    private void OnRevive()
    {
        // Implementation of OnRevive method
    }

    private void ResetCharacterTeleportFX()
    {
        // Implementation of ResetCharacterTeleportFX method
    }

    private void OnHitByTrain()
    {
        // Implementation of OnHitByTrain method
    }

    private void OnJump()
    {
        // Implementation of OnJump method
    }

    private void OnRoll()
    {
        // Implementation of OnRoll method
    }

    private void Character_OnHangtime()
    {
        // Implementation of Character_OnHangtime method
    }

    private void OnChangeIsGrounded(bool isGrounded)
    {
        // Implementation of OnChangeIsGrounded method
    }

    private void HandleOnTeleboardStart()
    {
        // Implementation of HandleOnTeleboardStart method
    }

    private void OnRouteDone()
    {
        // Implementation of OnRouteDone method
    }

    private void OnLanding()
    {
        // Implementation of OnLanding method
    }

    private void OnSwitchToRunning()
    {
        // Implementation of OnSwitchToRunning method
    }

    private void OnJetPack()
    {
        // Implementation of OnJetPack method
    }

    private void InitializeCharacterRenderingEffects()
    {
        // Implementation of InitializeCharacterRenderingEffects method
    }

    private void OnStageMenuSequence()
    {
        // Implementation of OnStageMenuSequence method
    }

    private void OnIntroRun()
    {
        // Implementation of OnIntroRun method
    }

    private void IsInGame_OnChange(bool isInGame)
    {
        // Implementation of IsInGame_OnChange method
    }

    private void SteamJumpOnStart()
    {
        // Implementation of SteamJumpOnStart method
    }

    private void SteamJumpOnHangTime()
    {
        // Implementation of SteamJumpOnHangTime method
    }

    private void SteamJumpOnStop()
    {
        // Implementation of SteamJumpOnStop method
    }
} 