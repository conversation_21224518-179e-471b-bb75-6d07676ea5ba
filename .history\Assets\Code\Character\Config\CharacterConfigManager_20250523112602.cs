using UnityEngine;
using System.Collections.Generic;

namespace RG.Characters.Config
{
    /// <summary>
    /// 角色配置管理器
    /// </summary>
    public class CharacterConfigManager : MonoBehaviour
    {
        private static CharacterConfigManager _instance;
        public static CharacterConfigManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("CharacterConfigManager");
                    _instance = go.AddComponent<CharacterConfigManager>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
        
        // 角色配置字典
        private Dictionary<string, CharacterConfig> _characterConfigs = new Dictionary<string, CharacterConfig>();
        private Dictionary<string, CharacterStateConfig> _stateConfigs = new Dictionary<string, CharacterStateConfig>();
        
        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            LoadAllConfigs();
        }
        
        /// <summary>
        /// 加载所有角色配置
        /// </summary>
        private void LoadAllConfigs()
        {
            // 加载所有角色基础配置
            var characterConfigs = Resources.LoadAll<CharacterConfig>("Config/Characters");
            foreach (var config in characterConfigs)
            {
                if (!string.IsNullOrEmpty(config.CharacterId))
                {
                    _characterConfigs[config.CharacterId] = config;
                }
            }
            
            // 加载所有角色状态配置
            var stateConfigs = Resources.LoadAll<CharacterStateConfig>("Config/Characters");
            foreach (var config in stateConfigs)
            {
                if (!string.IsNullOrEmpty(config.CharacterId))
                {
                    _stateConfigs[config.CharacterId] = config;
                }
            }
            
            Debug.Log($"已加载 {_characterConfigs.Count} 个角色配置和 {_stateConfigs.Count} 个状态配置");
        }
        
        /// <summary>
        /// 获取指定角色的基础配置
        /// </summary>
        public CharacterConfig GetCharacterConfig(string characterId)
        {
            if (_characterConfigs.TryGetValue(characterId, out var config))
            {
                return config;
            }
            
            Debug.LogError($"未找到角色 {characterId} 的基础配置");
            return null;
        }
        
        /// <summary>
        /// 获取指定角色的状态配置
        /// </summary>
        public CharacterStateConfig GetStateConfig(string characterId)
        {
            if (_stateConfigs.TryGetValue(characterId, out var config))
            {
                return config;
            }
            
            Debug.LogError($"未找到角色 {characterId} 的状态配置");
            return null;
        }
        
        /// <summary>
        /// 获取指定角色的状态转换配置
        /// </summary>
        public CharacterStateConfig.StateTransitionConfig GetStateTransition(string characterId, string fromState, string toState)
        {
            var stateConfig = GetStateConfig(characterId);
            if (stateConfig == null) return null;
            
            return stateConfig.GetStateTransition(fromState, toState);
        }
        
        /// <summary>
        /// 获取指定角色的状态行为配置
        /// </summary>
        public CharacterStateConfig.StateBehaviorConfig GetStateBehavior(string characterId, string stateName)
        {
            var stateConfig = GetStateConfig(characterId);
            if (stateConfig == null) return null;
            
            return stateConfig.GetStateBehavior(stateName);
        }
        
        /// <summary>
        /// 验证所有配置
        /// </summary>
        public bool ValidateAllConfigs()
        {
            bool isValid = true;
            
            foreach (var config in _characterConfigs.Values)
            {
                if (!ValidateCharacterConfig(config))
                {
                    isValid = false;
                }
            }
            
            foreach (var config in _stateConfigs.Values)
            {
                if (!ValidateStateConfig(config))
                {
                    isValid = false;
                }
            }
            
            return isValid;
        }
        
        /// <summary>
        /// 验证角色基础配置
        /// </summary>
        private bool ValidateCharacterConfig(CharacterConfig config)
        {
            if (string.IsNullOrEmpty(config.CharacterId))
            {
                Debug.LogError($"角色配置缺少CharacterId");
                return false;
            }
            
            if (config.BaseSpeed <= 0)
            {
                Debug.LogError($"角色 {config.CharacterId} 的基础速度必须大于0");
                return false;
            }
            
            if (config.Gravity <= 0)
            {
                Debug.LogError($"角色 {config.CharacterId} 的重力必须大于0");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 验证角色状态配置
        /// </summary>
        private bool ValidateStateConfig(CharacterStateConfig config)
        {
            if (string.IsNullOrEmpty(config.CharacterId))
            {
                Debug.LogError($"状态配置缺少CharacterId");
                return false;
            }
            
            if (config.StateTransitions == null || config.StateTransitions.Count == 0)
            {
                Debug.LogError($"角色 {config.CharacterId} 缺少状态转换配置");
                return false;
            }
            
            if (config.StateBehaviors == null || config.StateBehaviors.Count == 0)
            {
                Debug.LogError($"角色 {config.CharacterId} 缺少状态行为配置");
                return false;
            }
            
            return true;
        }
    }
} 
} 